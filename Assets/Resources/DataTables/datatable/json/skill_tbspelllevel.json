[{"id": 10001, "level": 1, "base_data": [[1001, 10], [1002, 3], [1003, 18], [1004, 0], [1005, 0], [1006, 0], [1007, 1], [1008, 0], [1009, 1], [1010, 0.5], [1011, 0], [1012, 0]], "extra_data": []}, {"id": 10002, "level": 1, "base_data": [[1001, 22], [1002, 27], [1003, 5], [1004, 0], [1005, 0], [1006, 0], [1007, 3], [1008, 0], [1009, 1], [1010, 4], [1011, 0], [1012, 0]], "extra_data": []}, {"id": 10003, "level": 1, "base_data": [[1001, 30], [1002, 9], [1003, 8], [1004, 0], [1005, 0], [1006, 60], [1007, 5], [1008, 0], [1009, 1], [1010, 0.5], [1011, 0], [1012, 0]], "extra_data": [["attract_radius", 10], ["split_time", -1], ["trace_speed_rate", 0.4]]}, {"id": 10003, "level": 2, "base_data": [[1001, 30], [1002, 9], [1003, 8], [1004, 0], [1005, 0], [1006, 60], [1007, 5], [1008, 0], [1009, 1], [1010, 0.5], [1011, 0], [1012, 0]], "extra_data": [["attract_radius", 10], ["split_time", 4], ["trace_speed_rate", 0.5]]}, {"id": 10004, "level": 1, "base_data": [[1001, 45], [1002, 15], [1003, 25], [1004, 0], [1005, 0], [1006, 0], [1007, 1], [1008, 0], [1009, 1], [1010, 1], [1011, 0], [1012, 0]], "extra_data": []}, {"id": 10005, "level": 1, "base_data": [[1001, 15], [1002, 9], [1003, 0], [1004, 0], [1005, 0], [1006, 0], [1007, 2], [1008, 0], [1009, 1], [1010, 5], [1011, 0], [1012, 0]], "extra_data": []}, {"id": 10006, "level": 1, "base_data": [[1001, 3], [1002, 15], [1003, 0], [1004, 0], [1005, 0], [1006, 0], [1007, 3], [1008, 0], [1009, 1], [1010, 1.5], [1011, 3], [1012, 10]], "extra_data": []}, {"id": 10007, "level": 1, "base_data": [[1001, 10], [1002, 8], [1003, 20], [1004, 30], [1005, -2], [1006, 90], [1007, 5], [1008, 0], [1009, 3], [1010, 0.5], [1011, 0], [1012, 0]], "extra_data": [["trace_speed_rate", 0.5], ["begin_trace_time", 1]]}, {"id": 10008, "level": 1, "base_data": [[1001, 8], [1002, 3], [1003, 0], [1004, 0], [1005, 0], [1006, 0], [1007, 1], [1008, 0], [1009, 1], [1010, 5], [1011, 1], [1012, 100]], "extra_data": []}]