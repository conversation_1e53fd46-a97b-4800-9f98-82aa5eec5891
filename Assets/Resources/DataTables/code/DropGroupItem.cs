
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using <PERSON><PERSON>;


namespace cfg
{
public sealed partial class DropGroupItem : Luban.BeanBase
{
    public DropGroupItem(ByteBuf _buf) 
    {
        Weight = _buf.ReadInt();
        ItemId = _buf.ReadInt();
        ItemCount = _buf.ReadInt();
    }

    public static DropGroupItem DeserializeDropGroupItem(ByteBuf _buf)
    {
        return new DropGroupItem(_buf);
    }

    /// <summary>
    /// 权重概率
    /// </summary>
    public readonly int Weight;
    /// <summary>
    /// 道具ID
    /// </summary>
    public readonly int ItemId;
    /// <summary>
    /// 道具数目
    /// </summary>
    public readonly int ItemCount;
   
    public const int __ID__ = 1446841315;
    public override int GetTypeId() => __ID__;

    public  void ResolveRef(Tables tables)
    {
    }

    public override string ToString()
    {
        return "{ "
        + "weight:" + Weight + ","
        + "itemId:" + ItemId + ","
        + "itemCount:" + ItemCount + ","
        + "}";
    }
}

}

