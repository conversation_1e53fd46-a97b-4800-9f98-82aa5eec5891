
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------



namespace cfg.skill
{ 
    public enum SpellAttribute
    {
        /// <summary>
        /// 伤害
        /// </summary>
        Damage = 1001,
        /// <summary>
        /// 耗蓝
        /// </summary>
        Consume = 1002,
        /// <summary>
        /// 速度
        /// </summary>
        Speed = 1003,
        /// <summary>
        /// 角速度
        /// </summary>
        AngularVelocity = 1004,
        /// <summary>
        /// 加速度
        /// </summary>
        Acceleration = 1005,
        /// <summary>
        /// 散射
        /// </summary>
        ScatteringDegree = 1006,
        /// <summary>
        /// 时长
        /// </summary>
        Duration = 1007,
        /// <summary>
        /// 法穿
        /// </summary>
        SpellPenetration = 1008,
        /// <summary>
        /// 释放数量
        /// </summary>
        SpawnNum = 1009,
        /// <summary>
        /// 范围
        /// </summary>
        Range = 1010,
        /// <summary>
        /// 折射次数
        /// </summary>
        RefractionNum = 1011,
        /// <summary>
        /// 折射半径
        /// </summary>
        RefractionRadius = 1012,
    }

} 

