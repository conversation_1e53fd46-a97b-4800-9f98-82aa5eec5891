
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using <PERSON><PERSON>;


namespace cfg.skill
{
public sealed partial class SpellLevel : Luban.BeanBase
{
    public SpellLevel(ByteBuf _buf) 
    {
        Id = _buf.ReadInt();
        Level = _buf.ReadInt();
        {int n0 = System.Math.Min(_buf.ReadSize(), _buf.Size);BaseData = new System.Collections.Generic.Dictionary<skill.SpellAttribute, float>(n0 * 3 / 2);for(var i0 = 0 ; i0 < n0 ; i0++) { skill.SpellAttribute _k0;  _k0 = (skill.SpellAttribute)_buf.ReadInt(); float _v0;  _v0 = _buf.ReadFloat();     BaseData.Add(_k0, _v0);}}
        {int n0 = System.Math.Min(_buf.ReadSize(), _buf.Size);ExtraData = new System.Collections.Generic.Dictionary<string, float>(n0 * 3 / 2);for(var i0 = 0 ; i0 < n0 ; i0++) { string _k0;  _k0 = _buf.ReadString(); float _v0;  _v0 = _buf.ReadFloat();     ExtraData.Add(_k0, _v0);}}
    }

    public static SpellLevel DeserializeSpellLevel(ByteBuf _buf)
    {
        return new skill.SpellLevel(_buf);
    }

    /// <summary>
    /// ID
    /// </summary>
    public readonly int Id;
    /// <summary>
    /// 等级
    /// </summary>
    public readonly int Level;
    public readonly System.Collections.Generic.Dictionary<skill.SpellAttribute, float> BaseData;
    public readonly System.Collections.Generic.Dictionary<string, float> ExtraData;
   
    public const int __ID__ = 1105618713;
    public override int GetTypeId() => __ID__;

    public  void ResolveRef(Tables tables)
    {
    }

    public override string ToString()
    {
        return "{ "
        + "id:" + Id + ","
        + "level:" + Level + ","
        + "baseData:" + Luban.StringUtil.CollectionToString(BaseData) + ","
        + "extraData:" + Luban.StringUtil.CollectionToString(ExtraData) + ","
        + "}";
    }
}

}

