
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using <PERSON><PERSON>;


namespace cfg.skill
{
public partial class TbSpellLevel
{
    private readonly System.Collections.Generic.List<skill.SpellLevel> _dataList;

    private System.Collections.Generic.Dictionary<(int, int), skill.SpellLevel> _dataMapUnion;

    public TbSpellLevel(ByteBuf _buf)
    {
        _dataList = new System.Collections.Generic.List<skill.SpellLevel>();
        
        for(int n = _buf.ReadSize() ; n > 0 ; --n)
        {
            skill.SpellLevel _v;
            _v = skill.SpellLevel.DeserializeSpellLevel(_buf);
            _dataList.Add(_v);
        }
        _dataMapUnion = new System.Collections.Generic.Dictionary<(int, int), skill.SpellLevel>();
        foreach(var _v in _dataList)
        {
            _dataMapUnion.Add((_v.Id, _v.Level), _v);
        }
    }

    public System.Collections.Generic.List<skill.SpellLevel> DataList => _dataList;

    public skill.SpellLevel Get(int id, int level) => _dataMapUnion.TryGetValue((id, level), out skill.SpellLevel __v) ? __v : null;
    
    public void ResolveRef(Tables tables)
    {
        foreach(var _v in _dataList)
        {
            _v.ResolveRef(tables);
        }
    }
}

}

