
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using <PERSON><PERSON>;


namespace cfg.skill
{
public partial class TbSpell
{
    private readonly System.Collections.Generic.Dictionary<int, skill.Spell> _dataMap;
    private readonly System.Collections.Generic.List<skill.Spell> _dataList;
    
    public TbSpell(ByteBuf _buf)
    {
        _dataMap = new System.Collections.Generic.Dictionary<int, skill.Spell>();
        _dataList = new System.Collections.Generic.List<skill.Spell>();
        
        for(int n = _buf.ReadSize() ; n > 0 ; --n)
        {
            skill.Spell _v;
            _v = skill.Spell.DeserializeSpell(_buf);
            _dataList.Add(_v);
            _dataMap.Add(_v.Id, _v);
        }
    }

    public System.Collections.Generic.Dictionary<int, skill.Spell> DataMap => _dataMap;
    public System.Collections.Generic.List<skill.Spell> DataList => _dataList;

    public skill.Spell GetOrDefault(int key) => _dataMap.TryGetValue(key, out var v) ? v : null;
    public skill.Spell Get(int key) => _dataMap[key];
    public skill.Spell this[int key] => _dataMap[key];

    public void ResolveRef(Tables tables)
    {
        foreach(var _v in _dataList)
        {
            _v.ResolveRef(tables);
        }
    }

}

}

