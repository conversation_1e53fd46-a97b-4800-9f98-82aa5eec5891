
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using <PERSON><PERSON>;


namespace cfg.skill
{
public sealed partial class BuffEffectPair : Luban.BeanBase
{
    public BuffEffectPair(ByteBuf _buf) 
    {
        Id = (skill.SpellAttribute)_buf.ReadInt();
        ValueOperator = (skill.BuffEffectOperator)_buf.ReadInt();
        Value = _buf.ReadFloat();
        EffectPoint = (skill.BuffEffectivePoint)_buf.ReadInt();
    }

    public static BuffEffectPair DeserializeBuffEffectPair(ByteBuf _buf)
    {
        return new skill.BuffEffectPair(_buf);
    }

    /// <summary>
    /// 法术属性表id
    /// </summary>
    public readonly skill.SpellAttribute Id;
    public readonly skill.BuffEffectOperator ValueOperator;
    /// <summary>
    /// 暂时全部用float, 后面看看要不要用luban多态
    /// </summary>
    public readonly float Value;
    public readonly skill.BuffEffectivePoint EffectPoint;
   
    public const int __ID__ = 2020380187;
    public override int GetTypeId() => __ID__;

    public  void ResolveRef(Tables tables)
    {
    }

    public override string ToString()
    {
        return "{ "
        + "id:" + Id + ","
        + "valueOperator:" + ValueOperator + ","
        + "value:" + Value + ","
        + "effectPoint:" + EffectPoint + ","
        + "}";
    }
}

}

