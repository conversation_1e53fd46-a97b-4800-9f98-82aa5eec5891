
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using <PERSON><PERSON>;


namespace cfg.skill
{
public sealed partial class BuffLevel : Luban.BeanBase
{
    public BuffLevel(ByteBuf _buf) 
    {
        Id = _buf.ReadInt();
        Level = _buf.ReadInt();
        {int __n0 = System.Math.Min(_buf.ReadSize(), _buf.Size);Effects = new skill.BuffEffectPair[__n0];for(var __index0 = 0 ; __index0 < __n0 ; __index0++) { skill.BuffEffectPair __e0;__e0 = skill.BuffEffectPair.DeserializeBuffEffectPair(_buf); Effects[__index0] = __e0;}}
        {int n0 = System.Math.Min(_buf.ReadSize(), _buf.Size);ExtraData = new System.Collections.Generic.Dictionary<string, float>(n0 * 3 / 2);for(var i0 = 0 ; i0 < n0 ; i0++) { string _k0;  _k0 = _buf.ReadString(); float _v0;  _v0 = _buf.ReadFloat();     ExtraData.Add(_k0, _v0);}}
    }

    public static BuffLevel DeserializeBuffLevel(ByteBuf _buf)
    {
        return new skill.BuffLevel(_buf);
    }

    /// <summary>
    /// ID
    /// </summary>
    public readonly int Id;
    /// <summary>
    /// 等级
    /// </summary>
    public readonly int Level;
    /// <summary>
    /// buff影响属性数组
    /// </summary>
    public readonly skill.BuffEffectPair[] Effects;
    /// <summary>
    /// 额外数据
    /// </summary>
    public readonly System.Collections.Generic.Dictionary<string, float> ExtraData;
   
    public const int __ID__ = 123200372;
    public override int GetTypeId() => __ID__;

    public  void ResolveRef(Tables tables)
    {
        foreach (var _e in Effects) { _e?.ResolveRef(tables); }
    }

    public override string ToString()
    {
        return "{ "
        + "id:" + Id + ","
        + "level:" + Level + ","
        + "effects:" + Luban.StringUtil.CollectionToString(Effects) + ","
        + "extraData:" + Luban.StringUtil.CollectionToString(ExtraData) + ","
        + "}";
    }
}

}

