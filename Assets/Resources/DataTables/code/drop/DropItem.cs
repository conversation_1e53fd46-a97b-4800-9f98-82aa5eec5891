
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using <PERSON><PERSON>;


namespace cfg.drop
{
public sealed partial class DropItem : Luban.BeanBase
{
    public DropItem(ByteBuf _buf) 
    {
        Id = _buf.ReadInt();
        {int n0 = System.Math.Min(_buf.ReadSize(), _buf.Size);RandomDropItem = new System.Collections.Generic.List<RandomItemGroup>(n0);for(var i0 = 0 ; i0 < n0 ; i0++) { RandomItemGroup _e0;  _e0 = RandomItemGroup.DeserializeRandomItemGroup(_buf); RandomDropItem.Add(_e0);}}
        {int n0 = System.Math.Min(_buf.ReadSize(), _buf.Size);RandomDropGroup = new System.Collections.Generic.List<DropGroup>(n0);for(var i0 = 0 ; i0 < n0 ; i0++) { DropGroup _e0;  _e0 = DropGroup.DeserializeDropGroup(_buf); RandomDropGroup.Add(_e0);}}
    }

    public static DropItem DeserializeDropItem(ByteBuf _buf)
    {
        return new drop.DropItem(_buf);
    }

    /// <summary>
    /// 编号
    /// </summary>
    public readonly int Id;
    /// <summary>
    /// 随机掉落道具组
    /// </summary>
    public readonly System.Collections.Generic.List<RandomItemGroup> RandomDropItem;
    public readonly System.Collections.Generic.List<DropGroup> RandomDropGroup;
   
    public const int __ID__ = -1779526943;
    public override int GetTypeId() => __ID__;

    public  void ResolveRef(Tables tables)
    {
        foreach (var _e in RandomDropItem) { _e?.ResolveRef(tables); }
        foreach (var _e in RandomDropGroup) { _e?.ResolveRef(tables); }
    }

    public override string ToString()
    {
        return "{ "
        + "id:" + Id + ","
        + "randomDropItem:" + Luban.StringUtil.CollectionToString(RandomDropItem) + ","
        + "randomDropGroup:" + Luban.StringUtil.CollectionToString(RandomDropGroup) + ","
        + "}";
    }
}

}

