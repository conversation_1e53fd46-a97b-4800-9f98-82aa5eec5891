
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using <PERSON><PERSON>;


namespace cfg.drop
{
public partial class TbDrop
{
    private readonly System.Collections.Generic.Dictionary<int, drop.DropItem> _dataMap;
    private readonly System.Collections.Generic.List<drop.DropItem> _dataList;
    
    public TbDrop(ByteBuf _buf)
    {
        _dataMap = new System.Collections.Generic.Dictionary<int, drop.DropItem>();
        _dataList = new System.Collections.Generic.List<drop.DropItem>();
        
        for(int n = _buf.ReadSize() ; n > 0 ; --n)
        {
            drop.DropItem _v;
            _v = drop.DropItem.DeserializeDropItem(_buf);
            _dataList.Add(_v);
            _dataMap.Add(_v.Id, _v);
        }
    }

    public System.Collections.Generic.Dictionary<int, drop.DropItem> DataMap => _dataMap;
    public System.Collections.Generic.List<drop.DropItem> DataList => _dataList;

    public drop.DropItem GetOrDefault(int key) => _dataMap.TryGetValue(key, out var v) ? v : null;
    public drop.DropItem Get(int key) => _dataMap[key];
    public drop.DropItem this[int key] => _dataMap[key];

    public void ResolveRef(Tables tables)
    {
        foreach(var _v in _dataList)
        {
            _v.ResolveRef(tables);
        }
    }

}

}

