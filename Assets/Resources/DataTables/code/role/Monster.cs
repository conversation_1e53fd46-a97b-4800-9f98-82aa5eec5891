
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using <PERSON><PERSON>;


namespace cfg.role
{
public sealed partial class Monster : Luban.BeanBase
{
    public Monster(ByteBuf _buf) 
    {
        Id = _buf.ReadInt();
        ResourceName = _buf.ReadString();
        DropItem = _buf.ReadInt();
    }

    public static Monster DeserializeMonster(ByteBuf _buf)
    {
        return new role.Monster(_buf);
    }

    /// <summary>
    /// 编号
    /// </summary>
    public readonly int Id;
    /// <summary>
    /// 资源名称
    /// </summary>
    public readonly string ResourceName;
    /// <summary>
    /// 掉落道具
    /// </summary>
    public readonly int DropItem;
   
    public const int __ID__ = -509306302;
    public override int GetTypeId() => __ID__;

    public  void ResolveRef(Tables tables)
    {
    }

    public override string ToString()
    {
        return "{ "
        + "id:" + Id + ","
        + "resourceName:" + ResourceName + ","
        + "dropItem:" + DropItem + ","
        + "}";
    }
}

}

