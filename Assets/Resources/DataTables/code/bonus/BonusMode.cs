
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using <PERSON><PERSON>;


namespace cfg.bonus
{
public sealed partial class BonusMode : Luban.BeanBase
{
    public BonusMode(ByteBuf _buf) 
    {
        Id = _buf.ReadInt();
        Mode = _buf.ReadInt();
        {int n0 = System.Math.Min(_buf.ReadSize(), _buf.Size);RandomDropLibrary = new System.Collections.Generic.Dictionary<int, int>(n0 * 3 / 2);for(var i0 = 0 ; i0 < n0 ; i0++) { int _k0;  _k0 = _buf.ReadInt(); int _v0;  _v0 = _buf.ReadInt();     RandomDropLibrary.Add(_k0, _v0);}}
        {int n0 = System.Math.Min(_buf.ReadSize(), _buf.Size);RandomShopLibrary = new System.Collections.Generic.Dictionary<int, int>(n0 * 3 / 2);for(var i0 = 0 ; i0 < n0 ; i0++) { int _k0;  _k0 = _buf.ReadInt(); int _v0;  _v0 = _buf.ReadInt();     RandomShopLibrary.Add(_k0, _v0);}}
    }

    public static BonusMode DeserializeBonusMode(ByteBuf _buf)
    {
        return new bonus.BonusMode(_buf);
    }

    /// <summary>
    /// 编号
    /// </summary>
    public readonly int Id;
    /// <summary>
    /// 奖励类型
    /// </summary>
    public readonly int Mode;
    /// <summary>
    /// 掉落随机库
    /// </summary>
    public readonly System.Collections.Generic.Dictionary<int, int> RandomDropLibrary;
    /// <summary>
    /// 商店随机库
    /// </summary>
    public readonly System.Collections.Generic.Dictionary<int, int> RandomShopLibrary;
   
    public const int __ID__ = -353771725;
    public override int GetTypeId() => __ID__;

    public  void ResolveRef(Tables tables)
    {
    }

    public override string ToString()
    {
        return "{ "
        + "id:" + Id + ","
        + "mode:" + Mode + ","
        + "randomDropLibrary:" + Luban.StringUtil.CollectionToString(RandomDropLibrary) + ","
        + "randomShopLibrary:" + Luban.StringUtil.CollectionToString(RandomShopLibrary) + ","
        + "}";
    }
}

}

