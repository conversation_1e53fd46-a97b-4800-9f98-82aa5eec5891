
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using <PERSON><PERSON>;


namespace cfg.item
{
public sealed partial class Weapon : Luban.BeanBase
{
    public Weapon(ByteBuf _buf) 
    {
        Id = _buf.ReadInt();
        Name = _buf.ReadString();
        Desc = _buf.ReadString();
        BaseProperties = MagicstaffBaseProperties.DeserializeMagicstaffBaseProperties(_buf);
        ExtraProperties = MagicstaffExtraProperties.DeserializeMagicstaffExtraProperties(_buf);
        SlotNum = _buf.ReadInt();
        Range = _buf.ReadFloat();
    }

    public static Weapon DeserializeWeapon(ByteBuf _buf)
    {
        return new item.Weapon(_buf);
    }

    /// <summary>
    /// 法杖ID
    /// </summary>
    public readonly int Id;
    /// <summary>
    /// 名称
    /// </summary>
    public readonly string Name;
    /// <summary>
    /// 描述
    /// </summary>
    public readonly string Desc;
    public readonly MagicstaffBaseProperties BaseProperties;
    /// <summary>
    /// 散射
    /// </summary>
    public readonly MagicstaffExtraProperties ExtraProperties;
    /// <summary>
    /// 槽位数目
    /// </summary>
    public readonly int SlotNum;
    /// <summary>
    /// 射程
    /// </summary>
    public readonly float Range;
   
    public const int __ID__ = -1736061577;
    public override int GetTypeId() => __ID__;

    public  void ResolveRef(Tables tables)
    {
        BaseProperties?.ResolveRef(tables);
        ExtraProperties?.ResolveRef(tables);
    }

    public override string ToString()
    {
        return "{ "
        + "id:" + Id + ","
        + "name:" + Name + ","
        + "desc:" + Desc + ","
        + "baseProperties:" + BaseProperties + ","
        + "extraProperties:" + ExtraProperties + ","
        + "slotNum:" + SlotNum + ","
        + "range:" + Range + ","
        + "}";
    }
}

}

