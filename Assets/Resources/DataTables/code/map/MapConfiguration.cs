
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using <PERSON><PERSON>;


namespace cfg.map
{
public sealed partial class MapConfiguration : Luban.BeanBase
{
    public MapConfiguration(ByteBuf _buf) 
    {
        Id = _buf.ReadInt();
        ResourceName = _buf.ReadString();
        MapType = _buf.ReadInt();
        {int n0 = System.Math.Min(_buf.ReadSize(), _buf.Size);MonsterRefreshesData = new System.Collections.Generic.List<System.Collections.Generic.List<MonsterKey>>(n0);for(var i0 = 0 ; i0 < n0 ; i0++) { System.Collections.Generic.List<MonsterKey> _e0;  {int n1 = System.Math.Min(_buf.ReadSize(), _buf.Size);_e0 = new System.Collections.Generic.List<MonsterKey>(n1);for(var i1 = 0 ; i1 < n1 ; i1++) { MonsterKey _e1;  _e1 = MonsterKey.DeserializeMonsterKey(_buf); _e0.Add(_e1);}} MonsterRefreshesData.Add(_e0);}}
    }

    public static MapConfiguration DeserializeMapConfiguration(ByteBuf _buf)
    {
        return new map.MapConfiguration(_buf);
    }

    /// <summary>
    /// 编号
    /// </summary>
    public readonly int Id;
    /// <summary>
    /// 资源名称
    /// </summary>
    public readonly string ResourceName;
    /// <summary>
    /// 地图类型: 普通房间
    /// </summary>
    public readonly int MapType;
    /// <summary>
    /// 怪物波次: 第一波怪物ID1-怪物位置,第一波 怪物ID2-怪物位置;第二波怪物ID1-怪物位置,第二波怪物ID2-怪物位置
    /// </summary>
    public readonly System.Collections.Generic.List<System.Collections.Generic.List<MonsterKey>> MonsterRefreshesData;
   
    public const int __ID__ = 1689123692;
    public override int GetTypeId() => __ID__;

    public  void ResolveRef(Tables tables)
    {
    }

    public override string ToString()
    {
        return "{ "
        + "id:" + Id + ","
        + "resourceName:" + ResourceName + ","
        + "mapType:" + MapType + ","
        + "monsterRefreshesData:" + Luban.StringUtil.CollectionToString(MonsterRefreshesData) + ","
        + "}";
    }
}

}

