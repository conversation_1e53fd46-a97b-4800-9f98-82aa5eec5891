{"name": "MoreMountains.TopDownEngine", "rootNamespace": "", "references": ["GUID:4a1cb1490dc4df8409b2580d6b44e75e", "GUID:8087d854c1f812e4b9a74437dfc524e0", "GUID:8a258d69670d9a54d943dd0e57ffea8a", "GUID:8f27f7a0f8da9854cb7c86f15d58fe82", "GUID:4307f53044263cf4b835bd812fc161a4", "GUID:d60799ab2a985554ea1a39cd38695018", "GUID:75469ad4d38634e559750d17036d5f7c", "GUID:ba880a3c55a41254a91d85dd28666dda", "GUID:1dbcca72f42070548a895178556c5bc3", "GUID:6055be8ebefd69e48b49212b09b47b2f", "GUID:68765d262e2128e4ab49c983f3411946", "GUID:72d1fea872bd7a449bf3818f2b0a6708"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [{"name": "com.unity.inputsystem", "expression": "1.0.0", "define": "MM_INPUTSYSTEM"}, {"name": "com.unity.cinemachine", "expression": "[1.0.0,3.0.0-pre]", "define": "MM_CINEMACHINE"}, {"name": "com.unity.postprocessing", "expression": "1.0.0", "define": "MM_POSTPROCESSING"}, {"name": "com.unity.textmeshpro", "expression": "1.0.0", "define": "MM_TEXTMESHPRO"}, {"name": "com.unity.cinemachine", "expression": "3.0.0", "define": "MM_CINEMACHINE3"}], "noEngineReferences": false}