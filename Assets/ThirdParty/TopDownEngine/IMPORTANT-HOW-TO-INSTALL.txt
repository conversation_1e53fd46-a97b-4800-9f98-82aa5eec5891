HOW TO INSTALL THE TOP DOWN ENGINE?
-----------------------------------

The engine relies on a few Unity packages to work, these are not installed by default by Unity, and will cause errors on install if you decide not to 
import them, that's completely normal.

To install the TopDown Engine, please follow these steps :

1. Create a new project from Unity Hub, pick the latest stable Unity release as your Unity version, and 2D or 3D as the Template
2. Go to the Asset Store window and import the project (it has to be in an empty project, not an existing one)
3. You will be warned that importing the TopDown Engine will overwrite your current project settings (that�s normal), click on Import.
4. This will take a bit of time.
5. Once this is complete, you�ll get a prompt saying �This Unity Package has Package Manager dependencies�. This is also normal, click on �Install/Upgrade�.
6. After that you�ll get a list of the contents of the engine, don�t touch anything, and click on Import in the bottom right corner.
7. Import will also take a while. Once import is complete, you�re ready to use the engine.

And that's all there is to it!
Have fun with the engine!
