using UnityEngine;
using UnityEngine.Events;

namespace MoreMountains.Tools
{
	/// <summary>
	/// Add this class to an object that you expect to pool from an objectPooler. 
	/// Note that these objects can't be destroyed by calling Destroy(), they'll just be set inactive (that's the whole point).
	/// </summary>
	[AddComponentMenu("More Mountains/Tools/Object Pool/MM Poolable Object")]
	public class MMPoolableObject : MMObjectBounds
	{
		[Header("Events")]
		public UnityEvent ExecuteOnEnable;
		public UnityEvent ExecuteOnDisable;
		
		public delegate void Events();
		public delegate void HitDeathEvents(GameObject go);
		public event Events OnSpawnComplete;

		
		private bool _haveInvokedBeforeDeath = false;
		public event HitDeathEvents BeforeDeath;
		
		[Header("Poolable Object")]
		/// The life time, in seconds, of the object. If set to 0 it'll live forever, if set to any positive value it'll be set inactive after that time.
		public float BirthTime = 0f;
        public float LifeTime = 0f;
		public float DeathTime = 0f;
		private float BeginTime = 0f;
		public float CurrentTime
		{
			get
			{
				if (Status < 1)
				{
					return 0;
				}
				return Time.time - BeginTime;
			}
		}

		protected int Status = 0;

        /// <summary>
        /// Turns the instance inactive, in order to eventually reuse it.
        /// </summary>
        public virtual void Destroy()
		{
			gameObject.SetActive(false);
		}

		/// <summary>
		/// Called every frame
		/// </summary>
		protected virtual void Update()
		{
			if (Status == 2)
			{
				if (CurrentTime >= BirthTime + LifeTime)
				{
					TryBeforeDeath();
				}

				if (CurrentTime >= BirthTime + LifeTime)
				{
					StageDeath();
				}
			}
		}

		/// <summary>
		/// When the objects get enabled (usually after having been pooled from an ObjectPooler, we initiate its death countdown.
		/// </summary>
		protected virtual void OnEnable()
		{
			Size = GetBounds().extents * 2;
			Invoke("StageBirth", 0);
            ExecuteOnEnable?.Invoke();
		}

		/// <summary>
		/// When the object gets disabled (maybe it got out of bounds), we cancel its programmed death
		/// </summary>
		protected virtual void OnDisable()
		{
			ExecuteOnDisable?.Invoke();
			CancelInvoke();
		}

		/// <summary>
		/// Triggers the on spawn complete event
		/// </summary>
		public virtual void TriggerOnSpawnComplete()
		{
			OnSpawnComplete?.Invoke();
		}

		/// <summary>
		/// 出生阶段
		/// </summary>
        public virtual void StageBirth()
        {
			Status = 1;
			BeginTime = Time.time;
			_haveInvokedBeforeDeath = false;
            Invoke("StageContinuous", BirthTime);
        }

		/// <summary>
		/// 持续阶段
		/// </summary>
        public virtual void StageContinuous()
        {
			Status = 2;
        }

		/// <summary>
		/// 消失阶段
		/// </summary>
        public virtual void StageDeath()
        {
	        if (Status == 3)
	        {
		        return;
	        }
			Status = 3;
            Invoke("Destroy", DeathTime);
        }

		protected virtual void TryBeforeDeath(GameObject hitGo = null)
		{
			if (!_haveInvokedBeforeDeath)
			{
				_haveInvokedBeforeDeath = true;
				//这里会执行分裂和buff属性影响
				BeforeDeath?.Invoke(hitGo);
			}
		}
    }
}