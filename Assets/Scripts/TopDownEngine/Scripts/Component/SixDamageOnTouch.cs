
using System.Collections.Generic;
using Battle.Utils;
using MoreMountains.Tools;
using MoreMountains.TopDownEngine;
using UnityEngine;


namespace Battle.MMCore
{
	public class SixDamageOnTouch : DamageOnTouch
	{
		[MMInspectorGroup("Damage Caused")]
		public float damageInterval;
		
		[MMInspectorGroup("Damage Caused")] 
		public bool damageCausedOneTime = false;
		
		[MMInspectorGroup("Damage Caused")] 
		public bool damageCausedOneTarget;

		
		[MMInspectorGroup("Feedbacks")]
		public bool genEffectOnHitObject = false;
		[MMInspectorGroup("Feedbacks")]
		[MMCondition(nameof(genEffectOnHitObject), true)]
		public GameObject hitObject;

		
		private Dictionary<Health, float> _lastHealthDamageTime = new Dictionary<Health, float>();
		private float _lastDamageTime = float.MinValue;
		protected override void OnEnable()
		{
			base.OnEnable();
			_lastHealthDamageTime.Clear();
			_lastDamageTime = float.MinValue;
		}

		/// <summary>
		/// Describes what happens when colliding with a damageable object
		/// </summary>
		/// <param name="health">Health.</param>
		protected override void OnCollideWithDamageable(Health health)
		{
			_collidingHealth = health;
			
			bool canDamage = false;
			if (damageCausedOneTime && damageCausedOneTarget)
			{
				canDamage = _lastHealthDamageTime.Count == 0;
			}
			else if(damageCausedOneTime && !damageCausedOneTarget)
			{
				canDamage = !_lastHealthDamageTime.ContainsKey(health);
			}
			else if (!damageCausedOneTime && damageCausedOneTarget)
			{
				canDamage = Time.time - _lastDamageTime > damageInterval;
			}
			else if (!damageCausedOneTime && !damageCausedOneTarget)
			{
				float lastHealthDamageTime = this._lastHealthDamageTime.GetValueOrDefault(health, float.MinValue);
				canDamage = Time.time - lastHealthDamageTime > damageInterval;
			}
			
			if (health.CanTakeDamageThisFrame() &&  canDamage)
			{
				_lastDamageTime = Time.time;
				_lastHealthDamageTime[health] = Time.time;
				// if what we're colliding with is a TopDownController, we apply a knockback force
				_colliderTopDownController = health.gameObject.MMGetComponentNoAlloc<TopDownController>();
				if (_colliderTopDownController == null)
				{
					_colliderTopDownController = health.gameObject.GetComponentInParent<TopDownController>();
				}

				HitDamageableFeedback?.PlayFeedbacks(this.transform.position);
				if (genEffectOnHitObject)
				{
					Battle.Utils.DebugUtil.LogInfo("SixDamageOnTouch: Trying to generate hit effect with name: {0}", hitObject.name);
					BattleHelper.GenHitGOOnTransform(health.gameObject, hitObject.name);
				}
				HitDamageableEvent?.Invoke(_colliderHealth);

				// we apply the damage to the thing we've collided with
				float randomDamage =
					UnityEngine.Random.Range(MinDamageCaused, Mathf.Max(MaxDamageCaused, MinDamageCaused));

				ApplyKnockback(randomDamage, TypedDamages);

				DetermineDamageDirection();

				if (RepeatDamageOverTime)
				{
					_colliderHealth.DamageOverTime(randomDamage, gameObject, InvincibilityDuration,
						InvincibilityDuration, _damageDirection, TypedDamages, AmountOfRepeats, DurationBetweenRepeats,
						DamageOverTimeInterruptible, RepeatedDamageType);
				}
				else
				{
					_colliderHealth.Damage(randomDamage, gameObject, InvincibilityDuration, InvincibilityDuration,
						_damageDirection, TypedDamages);
				}
			}
			
			// we apply self damage
			if (DamageTakenEveryTime + DamageTakenDamageable > 0 && !_colliderHealth.PreventTakeSelfDamage)
			{
				SelfDamage(DamageTakenEveryTime + DamageTakenDamageable);
			}
		}


		public void DamageDirectly(Health health)
		{
			if (health == null)
			{
				return;
			}
			float randomDamage =
				UnityEngine.Random.Range(MinDamageCaused, Mathf.Max(MaxDamageCaused, MinDamageCaused));
			if (RepeatDamageOverTime)
			{
				health.DamageOverTime(randomDamage, gameObject, InvincibilityDuration,
					InvincibilityDuration, _damageDirection, TypedDamages, AmountOfRepeats, DurationBetweenRepeats,
					DamageOverTimeInterruptible, RepeatedDamageType);
			}
			else
			{
				health.Damage(randomDamage, gameObject, InvincibilityDuration, InvincibilityDuration,
					_damageDirection, TypedDamages);
			}
		}
		
	}
}