using System;
using Battle.Spell;
using Battle.Utils;
using cfg.skill;
using MoreMountains.TopDownEngine;
using UnityEngine;

namespace Battle.Component
{
    [RequireComponent(typeof(SpellEntity))]
    public class TargetProjectTileExt : Projectile
    {
        [NonSerialized]
        public float FindTargetRange = float.MaxValue;
        [NonSerialized]
        public float AngularVelocity = 0;
        
        private GameObject _target;

        private SpellEntity _spellEntity;

        public RaycastHit2D hitResult;

        private TargetFindType TargetFindType => _spellEntity.TargetFindType;
        protected override void Awake()
        {
            base.Awake();
            _spellEntity = GetComponent<SpellEntity>();
        }

        private bool TryGetTarget()
        {
            if (_target == null)
            {
                if (TargetFindType is TargetFindType.Nearest or TargetFindType.MinDirection)//todo
                {
                    _target = ProgrammableWeaponCommonFunctions.FindNearestCharacter(transform.position, FindTargetRange);
                }
                else if(TargetFindType == TargetFindType.OtherHit)
                {
                    //不做事，当其他物体发生碰撞时自动赋值
                }
                else if (TargetFindType == cfg.skill.TargetFindType.Raycast)
                {
                    var range = _spellEntity.SpellBaseData.GetValue(SpellAttribute.Range);
                    #if UNITY_EDITOR
                    Debug.DrawRay(gameObject.transform.position, Direction * range, Color.blue, 1);
                    #endif
                    hitResult = Physics2D.Raycast(
                        new Vector2(gameObject.transform.position.x, gameObject.transform.position.y), 
                        new Vector2(Direction.x, Direction.y), range, _damageOnTouch.TargetLayerMask
                    );  
                    
                    if (hitResult.collider != null)
                    {
                        _target = hitResult.collider.gameObject;
                        transform.localScale = new Vector3(1, hitResult.distance / _spellEntity.prefabRange, 1);
                    }

                    _collider2D.enabled = false;
                }
            }
            return _target;
        }

        public void TrySetTarget(GameObject target, bool force = false)
        {
            if (!force && _target)
            {
                return;
            }

            _target = target;
        }
        

      
        private void ExecuteTraceMove()
        {
            TryGetTarget();
            if (_target)
            {
                var targetDirection = (_target.transform.position - transform.position).normalized;
                var angle = Vector2.Angle(Direction, targetDirection);
                var angleDelta = AngularVelocity * Speed * Time.deltaTime;
                if (angle < angleDelta)
                {
                    Direction = targetDirection;
                }
                else
                {
                    var cross = Vector3.Cross(Direction, targetDirection).z;
                    if (cross < 0)
                    {
                        angleDelta = -angleDelta;
                    }
                    SetDirection(Quaternion.Euler(new Vector3(0, 0, angleDelta)) * Direction);
                }
            }
          
            _movement = Direction * (Speed * Time.deltaTime);
            if (_rigidBody2D)
            {
                _rigidBody2D.MovePosition(this.transform.position + _movement);
            }
        }
        protected override void Initialization()
        {
            base.Initialization();
            _target = null;
            TryGetTarget();
        }
        
        
        public override void Movement()
        {
            //donothing
            //spellentity里驱动位移, 方便控制时序
        }

        public void MovementUpdate()
        {
            if (TargetFindType == TargetFindType.None)
            {
                if (_shouldMove)
                {
                    base.Movement();
                }
                return;
            }
            
            if (TargetFindType == TargetFindType.Raycast)
            {
                if (_target)
                {
                    _damageOnTouch.Colliding(_target);
                }
                else
                {
                    ///没目标直接回收
                    HitStageDeath(null);
                }
                return;
            }

            if (TargetFindType is TargetFindType.OtherHit or TargetFindType.MinDirection or TargetFindType.Nearest)
            {
                if (_shouldMove)
                {
                    ExecuteTraceMove();
                }
            }
        }
    }
}