using MoreMountains.TopDownEngine;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class SIXHealth : Health
{
    public override void Initialization()
    {
        base.Initialization();
    }

    public override void UpdateHealthBar(bool show)
    {
        UpdateHealthAnimationParameters();

        if (_healthBar != null)
        {
            _healthBar.UpdateBar(CurrentHealth, 0f, MaximumHealth, show);
        }

        if (MasterHealth == null)
        {
            if (_character != null)
            {
                if (_character.CharacterType == Character.CharacterTypes.Player)
                {
                    ScreenBase sb = GameUIManager.GetInstance().GetUI(typeof(MainLeftTopScene));
                    if (sb != null)
                    {
                        MainLeftTopScene leftTopScene = (MainLeftTopScene)sb;
                        leftTopScene.UpdateHealth(CurrentHealth, 0f, MaximumHealth);
                    }
                }
            }
        }
    }
}
