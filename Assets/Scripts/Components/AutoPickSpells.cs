using System;
using System.Collections;
using System.Collections.Generic;
using Battle;
using Battle.Utils;
using UnityEngine;

public class AutoPickSpells : BaseDropItem
{
    public MeshRenderer iconMeshRender;
    private Material _material;
    private void Awake()
    {
        _material = iconMeshRender.material;
    }

    public override int ItemID
    {
        get => base.ItemID;
        set
        {
            base.ItemID = value;
            var texture =
                Resources.Load<Texture>(
                    $"Items/Skill/{DataTableManager.Instance.Tables.TbItems.Get(base.ItemID)?.Icon}");
            _material.SetTexture("_MainTex", texture);
        }
    }

    void OnTriggerEnter2D(Collider2D other)
    {
        if (other.CompareTag("Player"))
        {
            // 获取物品ID
            ItemID = GetComponent<BaseDropItem>().ItemID;
            
            // 获取法术ID
            List<int> spellIDs = DataTableManager.Instance.Tables.TbItems.Get(ItemID).SubItem;
            BattleHelper.AddSlotElement(spellIDs[0]);

            // 销毁物品
            Destroy(gameObject);
        }
    }
}
