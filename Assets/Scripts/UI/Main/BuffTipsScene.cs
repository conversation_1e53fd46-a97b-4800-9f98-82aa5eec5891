using cfg.skill;
using MoreMountains.Tools;
using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class BuffTipsSceneParam : UIOpenScreenParameterBase
{
    public SlotElement slotElement;
    public BuffTipsSceneParam(SlotElement slotElement)
    {
        this.slotElement = slotElement;
    }
}


public class BuffTipsScene : ScreenBase
{
    BuffTipsCtrl mCtrl;
    BuffTipsSceneParam mParam;

    public BuffTipsScene(UIOpenScreenParameterBase param = null) : base(UIConst.UIBuffTips, param)
    {

    }

    public override void OnClose()
    {
        base.OnClose();
    }

    protected override void OnLoadSuccess()
    {
        base.OnLoadSuccess();
        mCtrl = mCtrlBase as BuffTipsCtrl;
        mParam = mOpenParam as BuffTipsSceneParam;

        UpdateTips();
    }

    void UpdateTips()
    {
        if (mParam.slotElement.Type == ElementType.Skill)
        {
            UpdateSkillTips();
        }
        else if (mParam.slotElement.Type == ElementType.Buff)
        {
            UpdateBuffTips();
        }
    }

    void UpdateSkillTips()
    {
        cfg.skill.Spell spell = DataTableManager.Instance.Tables.TbSpell.Get((int)mParam.slotElement.ID);
        mCtrl.BuffName.text = spell.Name;
        mCtrl.BuffSubType.text = CommonMapFunc.GetSpellSubTypeString(spell.Stype);
        //mCtrl.BuffQuality.text = "技能品质";
        //mCtrl.BuffItem.gameObject.SetActive(true);
        //mCtrl.BuffItem.Init(mParam.slotElement);
        var item = DataTableManager.Instance.Tables.TbItems.Get(spell.Id);
        Sprite newIconSprite = Resources.Load<Sprite>($"Items/Skill/{item?.Icon}");
        if (newIconSprite != null)
        {
            mCtrl.BuffItem.Icon.sprite = newIconSprite;
        }

        var descComponent = mCtrl.BuffEffectDesc[0].GetComponent<TextMeshProUGUI>();
        descComponent.text = String.Format(spell.Desc);
    }

    void UpdateBuffTips()
    {
        cfg.skill.Buff buff = DataTableManager.Instance.Tables.TbBuff.Get((int)mParam.slotElement.ID);

        mCtrl.BuffName.text = mParam.slotElement.ElementName;
        mCtrl.BuffSubType.text = CommonMapFunc.GetBuffSubTypeString(buff.Stype);
        //mCtrl.BuffQuality.text = "Buff品质";
        var item = DataTableManager.Instance.Tables.TbItems.Get(buff.Id);
        Sprite newIconSprite = Resources.Load<Sprite>($"Items/Skill/{item?.Icon}");
        if (newIconSprite != null)
        {
            mCtrl.BuffItem.Icon.sprite = newIconSprite;
        }

        var descComponent = mCtrl.BuffEffectDesc[0].GetComponent<TextMeshProUGUI>();
        descComponent.text = String.Format(buff.Desc);
    }

    protected override void UIAdapt(Vector2Int res)
    {
        // Debug.Log(string.Format("分辨率发生了变化，宽为{0},高为{1}", res.x, res.y));
    }

}
