using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System;

public class StaffSubScreen : SubScreenBase
{
    StaffSubCtrl mCtrl;
    WeaponItemSubScreen weaponItemSubScreen;
    StaffsocketsSubScreen staffsocketsSubScreen;
    public StaffSubScreen(StaffSubCtrl subCtrl) : base(subCtrl)
    {
    }

    protected override void Init()
    {
        base.Init();
        mCtrl = mCtrlBase as StaffSubCtrl;

        weaponItemSubScreen = new WeaponItemSubScreen(mCtrl.m_WeaponItemSubCtrl);
        staffsocketsSubScreen = new StaffsocketsSubScreen(mCtrl.m_staffsocketsSubCtrl);
    }

    public void UpdateWeaponItem(MagicStaff magicStaff)
    {
        weaponItemSubScreen.UpdateWeaponItem(magicStaff);
    }

    public void UpdateStaffSockets(MagicStaff magicStaff)
    {
        staffsocketsSubScreen.UpdateStaffSockets(magicStaff);
    }

    public void UpdateMagicSkills(List<SlotElement> slotElements)
    {
        staffsocketsSubScreen.UpdateMagicSkills(slotElements);
    }

    public List<GameObject> GetCurMagicStaffSlots()
    {
        return staffsocketsSubScreen.CurSlots;
    }

}