using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System;

public class WeaponItemSubScreen : SubScreenBase
{
    WeaponItemSubCtrl mCtrl;
    MagicStaff _magicStaff;
    public WeaponItemSubScreen(WeaponItemSubCtrl subCtrl) : base(subCtrl)
    {
    }

    protected override void Init()
    {
        base.Init();
        mCtrl = mCtrlBase as WeaponItemSubCtrl;

        mCtrl.OpenTipsBtn.onClick.AddListener(() =>
        {
            if (_magicStaff == null)
            {
                return;
            }
            var scene = GameUIManager.GetInstance().GetUI(typeof(BuffTipsScene));
            if (scene != null && scene.IsShow())
            {
                GameUIManager.GetInstance().CloseUI(typeof(BuffTipsScene));
            }

            GameUIManager.GetInstance().OpenUI(typeof(StaffTipsScene), new StaffTipsSceneParam(_magicStaff));
        });
    }

    public void UpdateWeaponItem(MagicStaff magicStaff)
    {
        _magicStaff = magicStaff;
        if (mCtrl != null && mCtrl.Icon != null)
        {
            // 测试: 资源放在Resources文件夹下
            Sprite newIconSprite = Resources.Load<Sprite>(string.Format("WeaponItem/{0}", magicStaff.Id));
            if (newIconSprite != null)
            {
                mCtrl.Icon.sprite = newIconSprite;
            }
            else
            {
                Debug.LogWarning("武器Icon资源未找到！");
            }
        }
        else
        {
            Debug.LogWarning("mCtrl或mCtrl.Background为空！");
        }

        if (mCtrl != null && mCtrl.Manabar != null)
        {
            mCtrl.Manabar.fillAmount = magicStaff.CurrentMagicValue * 1.0f / magicStaff.MaxMagicValue;
        }
        else
        {
            Debug.LogWarning("mCtrl或mCtrl.Manabar为空！");
        }
}

}