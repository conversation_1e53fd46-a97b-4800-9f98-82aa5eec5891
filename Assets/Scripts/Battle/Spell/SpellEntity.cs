using System;
using System.Collections.Generic;
using Battle.Component;
using Battle.Skill;
using cfg.skill;
using MoreMountains.Tools;
using MoreMountains.TopDownEngine;
using UnityEngine;
using UnityEngine.Serialization;

namespace Battle.Spell
{
    [RequireComponent(typeof(MMPoolableObject))]
    public class SpellEntity : MonoBehaviour
    {
        public float prefabRange = 1;
        public bool rangeX = true;
        public bool rangeY = true;
        protected TargetProjectTileExt Projectile;

        [NonSerialized]
        public TargetFindType TargetFindType = TargetFindType.None;

        public SpellLevel SpellLevel
        {
            private set;
            get;
        }
        
        public delegate void BeforeDeathExecute(GameObject go);
        public event BeforeDeathExecute BeforeDeathEvent;
        public SpellBaseData SpellBaseData
        {
            private set; 
            get;
        }

        public List<BuffElement> ActiveBuffs
        {
            private set;
            get;
        }
        public void Awake()
        {
            Projectile = GetComponent<TargetProjectTileExt>();
        }

        public virtual void OnEnable()
        {
            Projectile.BeforeDeath += ApplyBuffBeforeDeath;
        }

        public virtual void Update()
        {
            Projectile.Speed += SpellBaseData.GetValue(SpellAttribute.Acceleration) * Time.deltaTime;//简略的模拟加速度
            Projectile.MovementUpdate();
        }


        public void Set(SpellLevel spellLevel, SpellBaseData spellBaseData, List<BuffElement> activeBuffs)
        {
            SpellLevel = spellLevel;
            ActiveBuffs = activeBuffs;
            SpellBaseData = spellBaseData;
            var range = spellBaseData.GetValue(SpellAttribute.Range);
            var xScale = rangeX ? range / prefabRange : 1;
            var yScale = rangeY ? range / prefabRange : 1;
            transform.localScale = new Vector3(xScale, yScale, 1);
            TargetFindType = DataTableManager.Instance.Tables.TbSpell.Get(spellLevel.Id).TargetFindType;
        }

        private void ApplyBuffBeforeDeath(GameObject go)
        {
            if (ActiveBuffs != null)
            {
                foreach (var buffElement in ActiveBuffs)
                {
                    SpellBaseData.ApplyBuff(buffElement.BuffLevelCfg.Effects, BuffEffectivePoint.BeforeDeath);
                }
            }
            Projectile.LifeTime = SpellBaseData.GetValue(SpellAttribute.Duration);
            Projectile.Speed = SpellBaseData.GetValue(SpellAttribute.Speed);
            BeforeDeathEvent?.Invoke(go);
        }

        public void OnDisable()
        {
            Projectile.BeforeDeath -= ApplyBuffBeforeDeath;
        }

    }
}