using System;
using Battle.Skill;
using cfg.skill;

namespace Battle.Spell
{
    public class SpellEntity10007 : SpellEntity
    {
        private bool _haveUseTraceSpeed = false;

        public override void OnEnable()
        {
            base.OnEnable();
            _haveUseTraceSpeed = false;
        }
        public override void Update() 
        {
     
            if (!_haveUseTraceSpeed && Projectile.CurrentTime >= SpellLevel.GetExtraData(SpellLevel.BeginTraceTime))
            {
                _haveUseTraceSpeed = true;
                if (TargetFindType == TargetFindType.None)
                {
                    TargetFindType = TargetFindType.MinDirection;
                }
                Projectile.Speed *= SpellLevel.GetExtraData(SpellLevel.TraceSpeedRate);
            }
            base.Update();
        }
    }
}