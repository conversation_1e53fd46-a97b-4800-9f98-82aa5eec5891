using System.Collections.Generic;
using Battle.Component;
using Battle.Skill;
using Battle.Spell;
using cfg.skill;
using MoreMountains.Tools;
using MoreMountains.TopDownEngine;
using Unity.VisualScripting;
using UnityEditor.Experimental.GraphView;
using UnityEngine;

namespace Battle.Utils
{
    public static class BattleHelper
    {
        private static SkillElement CreateSkillElement(int spellId)
        {
            // switch (spellId)
            // {
            //     case 10001:
            //         return new SkillProjectile(spellId);
            //     case 10002:
            //         return new SkillBlackHole(spellId);
            //     case 10004:
            //         return new Spell10004(spellId);
            //     case 10005:
            //         return new Spell10005(spellId);
            //     case 10007:
            //         return new Spell10007(spellId);
            // }

            //DebugUtil.LogError("{0}未找到技能{1}", nameof(CreateSkillElement), spellId);
            return new DefaultSpellElement(spellId);
        }

        /// <summary>
        /// 当前本地玩家添加法术物品
        /// </summary>
        /// <param name="spellId"></param>
        public static void AddSlotElement(int spellId)
        {
            var spellType = DataTableManager.Instance.Tables.TbBuff.GetOrDefault(spellId) is not null
                ? ElementType.Buff
                : ElementType.Skill;
            if (spellType == ElementType.Skill)
            {
                var se = CreateSkillElement(spellId);
                if (se is null)
                {
                    DebugUtil.LogError("未找到技能{0}", spellId);
                    return;
                }

                se.SlotType = 2;
                PlayerData.GetInstance().AddSlotElement(se);
            }
            else
            {
                // Buff
                cfg.skill.Buff buff = DataTableManager.Instance.Tables.TbBuff.GetOrDefault(spellId);
                if (buff is null)
                {
                    DebugUtil.LogError("未找到buff{0}", spellId);
                    return;
                }

                // 使用 BuffFactory 创建对应类型的 Buff
                var buffElement = BuffFactory.CreateBuff(buff);
                if (buffElement is null)
                {
                    DebugUtil.LogError("未找到ID为{0}的Buff创建方法", spellId);
                    return;
                }

                buffElement.SlotType = 2;
                PlayerData.GetInstance().AddSlotElement(buffElement);
            }
        }


        /// <summary>
        /// 技能用生成弹道
        /// </summary>
        public static void SpawnProjectileEntity(SpellLevel spellLevel, GameObject owner,
            Vector3 targetDirection, string entityName,
            Vector3 spawnPosition, SpellBaseData spellBaseData = default,
            List<BuffElement> buffElements = null,
            bool scatterUniform = false, float offset = 0)
        {
            //出生前buff应用
            if (buffElements is not null)
            {
                foreach (var buffElement in buffElements)
                {
                    spellBaseData.ApplyBuff(buffElement.BuffLevelCfg.Effects, BuffEffectivePoint.BeforeBirth);
                }
            }

            

            var objectPool = SIXGameManager.Instance.skillPool;
            targetDirection = Vector3.Normalize(targetDirection);

            var scatter = spellBaseData.GetValue(SpellAttribute.ScatteringDegree);
            var spawnCount = spellBaseData.GetValue(SpellAttribute.SpawnNum);
            for (int i = 0; i < spawnCount; i++)
            {
                /// we get the next object in the pool and make sure it's not null
                GameObject nextGameObject = objectPool.GetPooledGameObjectOfType(entityName);
                // mandatory checks
                if (nextGameObject == null)
                {
                    DebugUtil.LogError("{0} doesnt have {1}", objectPool.name, entityName);
                    return;
                }

                if (nextGameObject.GetComponent<MMPoolableObject>() == null)
                {
                    DebugUtil.LogError(owner.name +
                                       " is trying to spawn objects that don't have a PoolableObject component.");
                    return;
                }

                var spellEntity = nextGameObject.GetComponent<SpellEntity>();
                if (spellEntity)
                {
                    spellEntity.Set(spellLevel, new SpellBaseData(spellBaseData), buffElements);
                }

                var damage = nextGameObject.GetComponent<DamageOnTouch>();
                if (damage)
                {
                    damage.MinDamageCaused = spellBaseData.GetValue(SpellAttribute.Damage);
                    damage.MaxDamageCaused = spellBaseData.GetValue(SpellAttribute.Damage);
                    damage.TypedDamages.Clear();
                }

                nextGameObject.transform.position = spawnPosition;
                TargetProjectTileExt projectile = nextGameObject.GetComponent<TargetProjectTileExt>();
                if (projectile != null)
                {
                    projectile.LifeTime = spellBaseData.GetValue(SpellAttribute.Duration);
                    projectile.Speed = spellBaseData.GetValue(SpellAttribute.Speed);
                    projectile.AngularVelocity = spellBaseData.GetValue(SpellAttribute.AngularVelocity);
                    Vector3 _randomSpreadDirection = Vector3.zero;

                    if (scatter == 0)
                    {
                        //do nothing
                    }
                    else if (scatterUniform)
                    {
                        _randomSpreadDirection.z =
                            MMMaths.Remap(i, 0, spawnCount - 1, -scatter / 2, scatter / 2);
                    }
                    else if (scatter != 0)
                    {
                        _randomSpreadDirection.z = Random.Range(-scatter / 2, scatter / 2);
                    }

                    Quaternion spread = Quaternion.Euler(_randomSpreadDirection);
                    projectile.SetDirection(spread * targetDirection);
                    nextGameObject.transform.position +=
                        projectile.Direction.normalized * offset;
                }


                nextGameObject.gameObject.SetActive(true);
                if (nextGameObject.GetComponent<MMPoolableObject>() != null)
                {
                    nextGameObject.GetComponent<MMPoolableObject>().TriggerOnSpawnComplete();
                }
                
                var refractionNum = (int)spellBaseData.GetValue(SpellAttribute.RefractionNum);
                var refractionComponent = refractionNum > 0
                    ? nextGameObject.GetOrAddComponent<RefractionComponent>()
                    : nextGameObject.GetComponent<RefractionComponent>();
                if (refractionComponent)
                {
                    refractionComponent.Init(new RefractionData()
                    {
                        RefractionCount = refractionNum,
                        RefractionRadius = spellBaseData.GetValue(SpellAttribute.RefractionRadius),
                    });
                }

                foreach (var buffElement in buffElements)
                {
                    buffElement.ApplyBuff(nextGameObject);
                }
            }
        }


        public static void GenHitGOOnTransform(GameObject owner, string entityName)
        {
            var objectPool = SIXGameManager.Instance.skillPool;

            // 调试：显示正在查找的对象名称和对象池状态
            DebugUtil.LogInfo("GenHitGOOnTransform: Looking for '{0}' in object pool", entityName);
            DebugUtil.LogInfo("GenHitGOOnTransform: Pool statistics: {0}", objectPool.GetPoolStatistics());

            GameObject nextGameObject = objectPool.GetPooledGameObjectOfType(entityName);
            // mandatory checks
            if (nextGameObject == null)
            {
                DebugUtil.LogError("{0} doesnt have {1}", objectPool.name, entityName);
                DebugUtil.LogInfo("GenHitGOOnTransform: Available objects in pool:");
                DebugUtil.LogInfo(objectPool.ListAllPooledObjects());
                return;
            }
            
            if (nextGameObject.GetComponent<MMPoolableObject>() == null)
            {
                DebugUtil.LogError(owner.name + " is trying to spawn objects that don't have a PoolableObject component.");
                return;
            }
            nextGameObject.transform.parent = owner.transform;
            nextGameObject.transform.localPosition = Vector3.zero;
            nextGameObject.gameObject.SetActive(true);
            
            if (nextGameObject.GetComponent<MMPoolableObject>() != null)
            {
                nextGameObject.GetComponent<MMPoolableObject>().TriggerOnSpawnComplete();
            }
        }

        public static void GenLinkGo(GameObject start, GameObject end, string entityName)
        {
            entityName = $"{entityName}Link";
            var objectPool = SIXGameManager.Instance.skillPool;
            GameObject nextGameObject = objectPool.GetPooledGameObjectOfType(entityName);
            if (nextGameObject == null)
            {
                DebugUtil.LogError("{0} doesnt have {1}", objectPool.name, entityName);
                return;
            }
            
            if (nextGameObject.GetComponent<MMPoolableObject>() == null)
            {
                DebugUtil.LogError($" is trying to spawn objects{nextGameObject.name} that don't have a PoolableObject component.");
                return;
            }

            var linkComponent = nextGameObject.GetComponent<LinkComponent>();
            if (!linkComponent)
            {
                DebugUtil.LogError($" {nextGameObject.name} that don't have a LinkComponent component.");
                return;
            }
            linkComponent.SetLink(start.transform, end.transform);
            nextGameObject.SetActive(true);
            if (nextGameObject.GetComponent<MMPoolableObject>() != null)
            {
                nextGameObject.GetComponent<MMPoolableObject>().TriggerOnSpawnComplete();
            }
        }
        
        
        public static void GenLinkGo(Vector3 start, Vector3 end, string entityName)
        {
            entityName = $"{entityName}Link";
            var objectPool = SIXGameManager.Instance.skillPool;
            GameObject nextGameObject = objectPool.GetPooledGameObjectOfType(entityName);
            if (nextGameObject == null)
            {
                DebugUtil.LogError("{0} doesnt have {1}", objectPool.name, entityName);
                return;
            }
            
            if (nextGameObject.GetComponent<MMPoolableObject>() == null)
            {
                DebugUtil.LogError($" is trying to spawn objects{nextGameObject.name} that don't have a PoolableObject component.");
                return;
            }

            var linkComponent = nextGameObject.GetComponent<LinkComponent>();
            if (!linkComponent)
            {
                DebugUtil.LogError($" {nextGameObject.name} that don't have a LinkComponent component.");
                return;
            }
            linkComponent.SetLinkPosition(start, end);
            nextGameObject.SetActive(true);
            if (nextGameObject.GetComponent<MMPoolableObject>() != null)
            {
                nextGameObject.GetComponent<MMPoolableObject>().TriggerOnSpawnComplete();
            }
        }
    }
}