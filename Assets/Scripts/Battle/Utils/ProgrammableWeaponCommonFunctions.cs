using System.Collections.Generic;
using Battle.Utils;
using MoreMountains.TopDownEngine;
using UnityEngine;

public class ProgrammableWeaponCommonFunctions
{
    public static GameObject FindNearestCharacter(Vector3 position, float range = float.MaxValue, string tag = "Enemy", HashSet<GameObject> excludeGameObject = null)
    {
        GameObject[] characters = GameObject.FindGameObjectsWithTag(tag);
        GameObject nearestCharacter = null;
        float minDistance = float.MaxValue;

        foreach (GameObject character in characters)
        {
            if (excludeGameObject is not null && excludeGameObject.Contains(character))
            {
                continue;
            }
            
            // Check if the enemy is alive
            var characterHealth = character.GetComponent<Health>(); // Replace with the actual component that checks health
            if (characterHealth != null && characterHealth.CurrentHealth > 0) // Replace IsAlive() with the actual method
            {
                float distance = Vector3.Distance(position, character.transform.position);

                if (distance < minDistance && distance <= range)
                {
                    minDistance = distance;
                    nearestCharacter = character;
                }
            }
        }

        return nearestCharacter;
    }

    
    // public static GameObject FindMinDirectionCharacter(Vector3 position, Vector3 towardDirection, string tag = "Enemy", HashSet<GameObject> excludeGameObject = null)
    // {
    //     GameObject[] characters = GameObject.FindGameObjectsWithTag(tag);
    //     GameObject result = null;
    //     float minAngle = float.MaxValue;
    //     float 
    //
    //     foreach (GameObject character in characters)
    //     {
    //         if (excludeGameObject is not null && excludeGameObject.Contains(character))
    //         {
    //             continue;
    //         }
    //         
    //         // Check if the enemy is alive
    //         var characterHealth = character.GetComponent<Health>(); // Replace with the actual component that checks health
    //         if (characterHealth != null && characterHealth.CurrentHealth > 0) // Replace IsAlive() with the actual method
    //         {
    //             float angle = MathUtil.DirectionToAngle(character.transform.position - position);
    //             float angle
    //         }
    //     }
    //
    //     return nearestCharacter;
    // }

    /// <summary>
    /// 查找范围内实体, todo 优化
    /// </summary>
    /// <param name="position"></param>
    /// <param name="name"></param>
    /// <param name="range"></param>
    /// <returns></returns>
    public static GameObject[] FindRangeEntity(Vector3 position, string name,float range = float.MaxValue)
    {
        var results = new List<GameObject>();

        //临时这样处理，后面要加实体管理、加阵营区分优化
        GameObject[] effects = GameObject.FindGameObjectsWithTag("Projectile");
        foreach (GameObject go in effects)
        {
            if (name == null || NamesMatch(go.name, name))
            {
                if (Vector3.Distance(position, go.transform.position) <= range)
                {
                    results.Add(go);
                }
            }
        }

        return results.ToArray();

    }

    /// <summary>
    /// Checks if two object names match, handling Unity's (Clone) suffix
    /// </summary>
    /// <param name="objectName">The actual object name (may have (Clone) suffix)</param>
    /// <param name="searchedName">The name we're searching for (without (Clone) suffix)</param>
    /// <returns>True if names match</returns>
    private static bool NamesMatch(string objectName, string searchedName)
    {
        if (string.IsNullOrEmpty(objectName) || string.IsNullOrEmpty(searchedName))
        {
            return false;
        }

        // Exact match
        if (objectName.Equals(searchedName))
        {
            return true;
        }

        // Handle Unity's (Clone) suffix
        if (objectName.EndsWith("(Clone)") && objectName.Length > 7)
        {
            string nameWithoutClone = objectName.Substring(0, objectName.Length - 7);
            return nameWithoutClone.Equals(searchedName);
        }

        return false;
    }
}