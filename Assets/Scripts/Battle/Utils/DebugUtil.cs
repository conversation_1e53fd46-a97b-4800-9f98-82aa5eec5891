#define ENABLE_LOG
using JetBrains.Annotations;
using UnityEngine;

namespace Battle.Utils
{
    public static class DebugUtil
    {
        public static void LogInfo(string info)
        {
#if ENABLE_LOG
            Debug.Log(info);
#endif
        }
        
        public static void LogInfo(string format, params object[] args)
        {
#if ENABLE_LOG
            Debug.LogFormat(format, args);
#endif
        }

        public static void LogWarning(string info)
        {
#if ENABLE_LOG
            Debug.LogWarning(info);
#endif
        }

        public static void LogWarning(string format, params object[] args)
        {
#if ENABLE_LOG
            Debug.LogWarningFormat(format, args);
#endif
        }
        
        public static void LogError(string info)
        {
#if ENABLE_LOG
            Debug.LogError(info);
#endif
        }
        
        public static void LogError(string format, params object[] args)
        {
#if ENABLE_LOG
            Debug.LogErrorFormat(format, args);
#endif
        }

        public static void LogWarning(string info)
        {
#if ENABLE_LOG
            Debug.LogWarning(info);
#endif
        }

        public static void LogWarning(string format, params object[] args)
        {
#if ENABLE_LOG
            Debug.LogWarningFormat(format, args);
#endif
        }
    }
}