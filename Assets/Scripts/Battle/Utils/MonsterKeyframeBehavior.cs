using MoreMountains.Tools;
using Spine.Unity;
using UnityEngine;
using static UnityEngine.UI.GridLayoutGroup;
using UnityEngine.Pool;
using System.Collections.Generic;
using MoreMountains.TopDownEngine;

public enum MonsterKeyFrameAttackType
{
    KEYFRAME_TRIGGER_HITBOX = 1, // 通过关键帧触发攻击盒
    KEYFRAME_TRIGGER_MISSILE, // 通过关键帧触发子弹
    KEYFRAME_TRIGGER_EFFECT, // 通过关键帧触发特效
}

public class MonsterKeyframeBehavior : MonoBehaviour
{
    public MonsterKeyFrameAttackType monsterKeyFrameAttackType = MonsterKeyFrameAttackType.KEYFRAME_TRIGGER_HITBOX;
    protected SkeletonMecanim _skeletonMecanim;
    protected Collider2D _attackHitBox;
    public List<GameObject> AttackEffects;
    private Health _myHealth;

    //临时放这里方便计算伤害
    private Health _characterHealth;
    private Transform _characterTransform;

    private Transform CharacterTransform
    {
        get
        {
            if (_characterTransform == null)
            {
                _characterTransform = GameObject.FindGameObjectWithTag("Player").transform;
                if (_characterTransform == null)
                {
                    Debug.LogError("未找到角色Transform脚本");
                }
            }
            return _characterTransform;
        }
    }
    private Health CharacterHealth
    {
        get
        {
            if (_characterHealth == null)
            {
                _characterHealth = GameObject.FindGameObjectWithTag("Player").GetComponent<Health>();
                if (_characterHealth == null)
                {
                    Debug.LogError("未找到角色Health脚本");
                }
            }
            return _characterHealth;
        }
    }
    
    private void Start()
    {
        _myHealth = GetComponentInParent<Health>();
        _skeletonMecanim = GetComponent<SkeletonMecanim>();
        _attackHitBox = GetComponentInChildren<Collider2D>();
        if (_attackHitBox != null)
        {
            _attackHitBox.enabled = false;
        }
    }

    void AttackStart()
    {
        if (monsterKeyFrameAttackType == MonsterKeyFrameAttackType.KEYFRAME_TRIGGER_HITBOX)
        {
            _attackHitBox.enabled = true;
        }
        else if (monsterKeyFrameAttackType == MonsterKeyFrameAttackType.KEYFRAME_TRIGGER_MISSILE)
        {
            // step : 查找指定对象池
            GameObject poolGameObject = GameObject.FindGameObjectWithTag("GameObjectPooler");
            if (poolGameObject == null)
            {
                Debug.LogError("Not Found GameObjectPooler");
                return;
            }
            MMSimpleObjectPooler[] allPoolers = poolGameObject.GetComponents<MMSimpleObjectPooler>();
            MMSimpleObjectPooler mMSimpleObjectPooler = null;
            foreach (MMSimpleObjectPooler pool in allPoolers)
            {
                if (pool.GameObjectToPool.name == "Missile")
                {
                    // 找到目标脚本
                    mMSimpleObjectPooler = pool;
                    break;
                }
            }
            MonsterSkillMissile.SpawnMissile(gameObject, mMSimpleObjectPooler, transform.position, 0, 1, true);
        }
    }

    void AttackEnd()
    {
        if (monsterKeyFrameAttackType == MonsterKeyFrameAttackType.KEYFRAME_TRIGGER_HITBOX)
        {
            _attackHitBox.enabled = false;
        }
        else if (monsterKeyFrameAttackType == MonsterKeyFrameAttackType.KEYFRAME_TRIGGER_MISSILE)
        {
            
        }
    }

    void testevent()
    {
        Debug.Log("testevent");
    }

    void bs1000_Attack1Start()
    {
        if (AttackEffects.Count < 1)
            return;

        if (monsterKeyFrameAttackType == MonsterKeyFrameAttackType.KEYFRAME_TRIGGER_EFFECT)
        {
            AttackEffects[0].SetActive(true);
            // Replay all particle systems on child objects
            ReplayParticleSystems(AttackEffects[0]);
            
            // 启用碰撞盒以造成伤害
            if (_attackHitBox != null)
            {
                _attackHitBox.enabled = true;
            }
        }
    }

    void bs1000_Attack1End()
    {
        if (AttackEffects.Count < 1)
            return;

        if (monsterKeyFrameAttackType == MonsterKeyFrameAttackType.KEYFRAME_TRIGGER_EFFECT)
        {
            AttackEffects[0].SetActive(false);
            
            // 禁用碰撞盒
            if (_attackHitBox != null)
            {
                _attackHitBox.enabled = false;
            }
        }
    }

    void bs1000_Attack2Start()
    {
        if (AttackEffects.Count < 2)
            return;

        if (monsterKeyFrameAttackType == MonsterKeyFrameAttackType.KEYFRAME_TRIGGER_EFFECT)
        {
            AttackEffects[1].SetActive(true);
            // Replay all particle systems on child objects
            ReplayParticleSystems(AttackEffects[1]);
            
            // 启用碰撞盒以造成伤害
            if (_attackHitBox != null)
            {
                _attackHitBox.enabled = true;
            }
        }

    }

    void bs1000_Attack2End()
    {
        if (AttackEffects.Count < 2)
            return;

        if (monsterKeyFrameAttackType == MonsterKeyFrameAttackType.KEYFRAME_TRIGGER_EFFECT)
        {
            AttackEffects[1].SetActive(false);
            
            // 禁用碰撞盒
            if (_attackHitBox != null)
            {
                _attackHitBox.enabled = false;
            }
        }
    }

    void bs1000_skill2AttackStart()
    {
        _attackHitBox.enabled = true;
    }

    void bs1000_skill2AttackEnd()
    {
        _attackHitBox.enabled = false;
    }

    void ms1003_AttackStart()
    {
        if (AttackEffects.Count > 0 && monsterKeyFrameAttackType == MonsterKeyFrameAttackType.KEYFRAME_TRIGGER_EFFECT)
        {
            AttackEffects[0].SetActive(true);
            ReplayParticleSystems(AttackEffects[0]);
        }
    }

    void ms1003_AttackEnd()
    {
        if (Vector3.Distance(CharacterTransform.position, transform.position) < 4.5)
        {
            //Debug.Log("ms1003_AttackExecute");
            CharacterHealth.Damage(5, this.gameObject, 0, 0, new Vector3(0, 0, 0));
        }
        _myHealth.Damage(float.MaxValue, this.gameObject, 0, 0, new Vector3(0, 0, 0));
    }

    /// <summary>
    /// Replays all particle systems attached to the given GameObject and its children
    /// </summary>
    /// <param name="effectObject">The GameObject containing particle systems to replay</param>
    private void ReplayParticleSystems(GameObject effectObject)
    {
        // Get all particle systems on the effect object and its children
        ParticleSystem[] particleSystems = effectObject.GetComponentsInChildren<ParticleSystem>(true);
        
        // Restart each particle system
        foreach (ParticleSystem ps in particleSystems)
        {
            ps.Stop(true); // Stop and clear the particle system
            ps.Play(true); // Play the particle system from the beginning
        }
    }
}