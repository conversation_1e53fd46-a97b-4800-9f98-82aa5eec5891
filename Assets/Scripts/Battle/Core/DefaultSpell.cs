using System.Collections.Generic;
using Battle.Utils;
using cfg.skill;
using MoreMountains.Tools;
using UnityEngine;

namespace Battle.Skill
{
    /// <summary>
    /// 滚石
    /// </summary>
    public sealed class DefaultSpellElement : SkillElement
    {
        private MMMultipleObjectPooler _objectPooler;
        private GameObject _owner;
        private List<BuffElement> _activeBuffs;
        private SpellLevel _skillLevelCfg => DataTableManager.Instance.Tables.TbSpellLevel.Get(ID, 1);
        private cfg.skill.Spell _skillCfg => DataTableManager.Instance.Tables.TbSpell.Get(ID);

        private SpellBaseData _spellBaseData;
        public override int Consume => (int)_spellBaseData.GetValue(SpellAttribute.Consume); 
        
        public DefaultSpellElement(int id)
        {
            ID = id;
            Type = ElementType.Skill;
            ElementName = $"法术：{_skillCfg.Name}";
            _spellBaseData = new SpellBaseData(_skillLevelCfg);
        }
        

        public override void ExecuteSkill(GameObject owner, Transform spawnPoint, List<BuffElement> activeBuffs)
        {
            var target = ProgrammableWeaponCommonFunctions.FindNearestCharacter(spawnPoint.position);
            var direction = target
                ? target.transform.position - spawnPoint.position
                : spawnPoint.position - owner.transform.position;

            _owner = owner;
          
            
            BattleHelper.SpawnProjectileEntity(
                _skillLevelCfg,
                _owner,
                direction,
                $"Spell{_skillCfg.Id.ToString()}",
                spawnPoint.position,
                new SpellBaseData(_spellBaseData), //目前先克隆，因为存在修改的可能
                new List<BuffElement>(_activeBuffs)
            );
        }

        public override void SetObjectPool(MMMultipleObjectPooler mObjectPooler)
        {
            base.SetObjectPool(mObjectPooler);
            _objectPooler = mObjectPooler;
        }

        public override void SaveBuffs(List<BuffElement> activeBuffs)
        {
            base.SaveBuffs(activeBuffs);
            _activeBuffs = activeBuffs;
        }

        /// <summary>
        /// 出生后buff应用
        /// </summary>
        /// <param name="skillGameObject"></param>
        private void ApplyBuffs(GameObject skillGameObject)
        {
            foreach (var buff in _activeBuffs)
            {
                buff.ApplyBuff(skillGameObject);
            }
        }

        private void ApplyBuffs(SkillElement skillElement)
        {
            foreach (var buff in _activeBuffs)
            {
                buff.ApplyBuff(skillElement);
            }
        }
    }
}