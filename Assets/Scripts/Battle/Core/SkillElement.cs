// 技能元素基类
using System.Collections.Generic;
using UnityEngine;
using MoreMountains.Tools;


public abstract class SkillElement : SlotElement
{
    //public float Cooldown = 1f;
    //public GameObject SkillPrefab;

    public virtual void InitSkillProperties() { }

    public virtual void ExecuteSkill(GameObject owner, Transform spawnPoint, List<BuffElement> activeBuffs) {}
    public virtual void SetObjectPool(MMMultipleObjectPooler mObjectPooler) { }
    public virtual void SaveBuffs(List<BuffElement> buffs) { }

}