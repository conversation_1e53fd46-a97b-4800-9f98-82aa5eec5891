// Buff元素基类

using System.Data;
using Battle.Skill;
using cfg.skill;
using UnityEngine;


public abstract class BuffElement : SlotElement
{
    public int SType = 0; // 0 未定义
    public virtual void ApplyBuff(GameObject skillGameObject) { }
    public virtual void ApplyBuff(SkillElement skill) { }
    public BuffLevel BuffLevelCfg => DataTableManager.Instance.Tables.TbBuffLevel.Get(ID, 1);
    public Buff BuffCfg => DataTableManager.Instance.Tables.TbBuff.Get(ID);
}