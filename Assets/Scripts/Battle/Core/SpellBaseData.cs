using System.Collections.Generic;
using cfg;
using cfg.skill;
using Unity.VisualScripting;

namespace Battle.Skill
{
    /// <summary>
    /// 可能被buff影响的基础法术数据
    /// </summary>
    public struct SpellBaseData
    {
        private Dictionary<SpellAttribute, float> _baseData;

        public SpellLevel SpellLevelCfg
        {
            private set;
            get;
        }
        public SpellBaseData(SpellLevel cfg)
        {
            SpellLevelCfg = cfg;
            // 防止 cfg.BaseData 为 null
            if (cfg?.BaseData != null)
            {
                _baseData = new Dictionary<SpellAttribute, float>(cfg.BaseData);
            }
            else
            {
                _baseData = new Dictionary<SpellAttribute, float>();
            }
        }

        public SpellBaseData(SpellBaseData baseData)
        {
            SpellLevelCfg = baseData.SpellLevelCfg;
            // 防止源数据的 _baseData 为 null
            if (baseData._baseData != null)
            {
                _baseData = new Dictionary<SpellAttribute, float>(baseData._baseData);
            }
            else
            {
                _baseData = new Dictionary<SpellAttribute, float>();
            }
        }

        public void Reset()
        {
            // 确保 _baseData 已初始化
            if (_baseData == null)
            {
                _baseData = new Dictionary<SpellAttribute, float>();
            }
            else
            {
                _baseData.Clear();
            }

            if (SpellLevelCfg?.BaseData != null)
            {
                foreach (var keyValuePair in SpellLevelCfg.BaseData)
                {
                    _baseData.Add(keyValuePair.Key, keyValuePair.Value);
                }
            }
        }
        public void ApplyBuff(BuffEffectPair[] buffEffectPairs, BuffEffectivePoint point)
        {
            // 确保 _baseData 已初始化
            if (_baseData == null)
            {
                _baseData = new Dictionary<SpellAttribute, float>();
            }

            foreach (var buffEffectPair in buffEffectPairs)
            {
                if (buffEffectPair.EffectPoint != point)
                {
                    continue;
                }
                float value = _baseData.GetValueOrDefault(buffEffectPair.Id, 0);
                switch (buffEffectPair.ValueOperator)
                {
                    case BuffEffectOperator.ADD:
                        value += buffEffectPair.Value;
                        break;
                    case BuffEffectOperator.MUL:
                        value *= buffEffectPair.Value;
                        break;
                    case BuffEffectOperator.SET:
                        value = buffEffectPair.Value;
                        break;
                }

                _baseData[buffEffectPair.Id] = value;
            }
        }
        
        public void ApplyBuff(SpellAttribute spellAttribute, BuffEffectOperator buffEffectOperator, float effectValue)
        {
            // 确保 _baseData 已初始化
            if (_baseData == null)
            {
                _baseData = new Dictionary<SpellAttribute, float>();
            }

            float value = _baseData.GetValueOrDefault(spellAttribute, 0);
            switch (buffEffectOperator)
            {
                case BuffEffectOperator.ADD:
                    value += effectValue;
                    break;
                case BuffEffectOperator.MUL:
                    value *= effectValue;
                    break;
                case BuffEffectOperator.SET:
                    value = effectValue;
                    break;
            }
            _baseData[spellAttribute] = value;
        }

        public float GetValue(SpellAttribute spellAttribute)
        {
            // 防止 _baseData 为 null 的情况
            if (_baseData == null)
            {
                return 0;
            }
            return _baseData.GetValueOrDefault(spellAttribute, 0);
        }
    }
}