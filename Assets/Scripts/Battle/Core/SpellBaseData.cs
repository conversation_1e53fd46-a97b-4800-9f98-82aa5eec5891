using System.Collections.Generic;
using cfg;
using cfg.skill;
using Unity.VisualScripting;

namespace Battle.Skill
{
    /// <summary>
    /// 可能被buff影响的基础法术数据
    /// </summary>
    public struct SpellBaseData
    {
        private Dictionary<SpellAttribute, float> _baseData;

        public SpellLevel SpellLevelCfg
        {
            private set;
            get;
        }
        public SpellBaseData(SpellLevel cfg)
        {
            _baseData = new Dictionary<SpellAttribute, float>(cfg.BaseData);
            SpellLevelCfg = cfg;
        }

        public SpellBaseData(SpellBaseData baseData)
        {
            SpellLevelCfg = baseData.SpellLevelCfg;
            _baseData = new Dictionary<SpellAttribute, float>(baseData._baseData);
        }

        public void Reset()
        {
            _baseData.Clear();
            foreach (var keyValuePair in SpellLevelCfg.BaseData)
            {
                _baseData.Add(keyValuePair.Key, keyValuePair.Value);
            }
        }
        public void ApplyBuff(BuffEffectPair[] buffEffectPairs, BuffEffectivePoint point)
        {
            foreach (var buffEffectPair in buffEffectPairs)
            {
                if (buffEffectPair.EffectPoint != point)
                {
                    continue;
                }
                float value = _baseData.GetValueOrDefault(buffEffectPair.Id, 0);
                switch (buffEffectPair.ValueOperator)
                {
                    case BuffEffectOperator.ADD:
                        value += buffEffectPair.Value;
                        break;
                    case BuffEffectOperator.MUL:
                        value *= buffEffectPair.Value;
                        break;
                    case BuffEffectOperator.SET:
                        value = buffEffectPair.Value;
                        break;
                }
            
                _baseData[buffEffectPair.Id] = value;
            }
        }
        
        public void ApplyBuff(SpellAttribute spellAttribute, BuffEffectOperator buffEffectOperator, float effectValue)
        {
            float value = _baseData.GetValueOrDefault(spellAttribute, 0);
            switch (buffEffectOperator)
            {
                case BuffEffectOperator.ADD:
                    value += effectValue;
                    break;
                case BuffEffectOperator.MUL:
                    value *= effectValue;
                    break;
                case BuffEffectOperator.SET:
                    value = effectValue;
                    break;
            }
            _baseData[spellAttribute] = value;
        }

        public float GetValue(SpellAttribute spellAttribute)
        {
            return _baseData.GetValueOrDefault(spellAttribute, 0);
        }
    }
}