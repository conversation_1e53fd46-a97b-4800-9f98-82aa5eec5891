using System.Collections.Generic;
using Battle.MMCore;
using Battle.Utils;
using MoreMountains.TopDownEngine;
using UnityEngine;

namespace Battle.Component
{
    public struct RefractionData
    {
        public int RefractionCount;
        public float RefractionRadius;
    }
    public class RefractionComponent : MonoBehaviour
    {        
        private RefractionData _refractionData;
        private HashSet<GameObject> _cachedTargets = new HashSet<GameObject>();
        private SixDamageOnTouch _sixDamageOnTouch;
        private TargetProjectTileExt _targetProjectTileExt;
        
        public void Awake()
        {
            _sixDamageOnTouch = GetComponent<SixDamageOnTouch>();
            _targetProjectTileExt = GetComponent<TargetProjectTileExt>();
        }

        public void Init(RefractionData refractionData)
        {
            _refractionData = refractionData;
        }

        public void OnEnable()
        {
            //生命周期要整理，这里伤害限制了要在Refraction OnEnable后才能计算
            if (_sixDamageOnTouch)
            {
                _sixDamageOnTouch.HitDamageableEvent.AddListener(OnHit);
            }
        }

        private void OnHit(Health target)
        {
            var lastRefractionGo = target.gameObject;
            var lastPosition =  lastRefractionGo.transform.position;
            var usePosition = false;
            if (_targetProjectTileExt && _targetProjectTileExt.hitResult.collider)
            {
                usePosition = true;
                lastPosition = gameObject.transform.position + _targetProjectTileExt.Direction.normalized * _targetProjectTileExt.hitResult.distance;
            }
            
            _cachedTargets.Clear();

            for (int i = 0; i < _refractionData.RefractionCount; i++)
            {
                _cachedTargets.Add(lastRefractionGo);
                var thisRefractionGo = ProgrammableWeaponCommonFunctions.FindNearestCharacter(lastRefractionGo.transform.position,
                    _refractionData.RefractionRadius,"Enemy", _cachedTargets);
                if (!thisRefractionGo)
                {
                    return;
                }
                //计算伤害
                _sixDamageOnTouch.DamageDirectly(thisRefractionGo.GetComponent<Health>());

                if (_sixDamageOnTouch.genEffectOnHitObject)
                {
                    //生成受击特效
                    BattleHelper.GenHitGOOnTransform(thisRefractionGo, _sixDamageOnTouch.hitObject.name);
                }

                //生成链接反射特效
                if (usePosition)
                {
                    // todo 射线或者通过碰撞体组件获取折射点
                    BattleHelper.GenLinkGo(lastPosition, thisRefractionGo.transform.position, gameObject.name);
                }
                else
                {
                    BattleHelper.GenLinkGo(lastRefractionGo, thisRefractionGo, gameObject.name);
                }
                
                
                lastRefractionGo = thisRefractionGo;
            }
        }

        public void OnDisable()
        {
            if (_sixDamageOnTouch)
            {
                _sixDamageOnTouch.HitDamageableEvent.RemoveListener(OnHit);
            }
        }
    }
}