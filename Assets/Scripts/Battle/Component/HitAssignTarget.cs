using System;
using Battle.MMCore;
using Battle.Spell;
using cfg.skill;
using MoreMountains.TopDownEngine;
using UnityEngine;

namespace Battle.Component
{
    [RequireComponent(typeof(SixDamageOnTouch))]
    [RequireComponent(typeof(SpellEntity))]
    public class HitAssignTarget : MonoBehaviour
    {
        private SpellLevel SpellLevel
        {
            set;
            get;
        }
        private SixDamageOnTouch _sixDamageOnTouch;
        private SpellEntity _spellEntity;

        public void Awake()
        {
            _sixDamageOnTouch = GetComponent<SixDamageOnTouch>();
            _spellEntity = GetComponent<SpellEntity>();
        }

        public void OnEnable()
        {
            if (_sixDamageOnTouch)
            {
                _sixDamageOnTouch.HitDamageableEvent.AddListener(OnHit);
            }
        }

        public void SetSpellLevel(SpellLevel spellLevel)
        {
            SpellLevel = spellLevel;   
        }

        private void OnHit(Health target)
        {
            var results = ProgrammableWeaponCommonFunctions.FindRangeEntity(transform.position, gameObject.name,
                _spellEntity.SpellLevel.GetExtraData(SpellLevel.AttractRadius));
            foreach (var result in results)
            {
                var projectTileExt = result.GetComponent<TargetProjectTileExt>();
                projectTileExt.Speed *= _spellEntity.SpellLevel.GetExtraData(SpellLevel.TraceSpeedRate);
                if (projectTileExt)
                {
                    projectTileExt.TrySetTarget(target.gameObject);
                }
            }
        }

        public void OnDisable()
        {
            if (_sixDamageOnTouch)
            {
                _sixDamageOnTouch.HitDamageableEvent.RemoveListener(OnHit);
            }
        }
    }
}