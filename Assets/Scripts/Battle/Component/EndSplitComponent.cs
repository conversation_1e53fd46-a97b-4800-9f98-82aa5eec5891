using System;
using System.Collections.Generic;
using Battle.Skill;
using Battle.Spell;
using Battle.Utils;
using cfg.skill;
using MoreMountains.TopDownEngine;
using UnityEditor.SceneManagement;
using UnityEngine;
using UnityEngine.Pool;
using Random = UnityEngine.Random;

namespace Battle.Component
{
    /// <summary>
    /// 目前用于buff20003
    /// </summary>
    public class EndSplitComponent : MonoBehaviour
    {
        private const bool _uniformScatter = true;
        private SpellEntity _spellEntity;
        private Projectile _projectile;
        private BuffLevel _buffLevelCfg;

        private bool _active;
        public void Awake()
        {
            _spellEntity = GetComponent<SpellEntity>();
            _projectile = GetComponent<Projectile>();
        }

        public void Set(BuffLevel buffLevel)
        {
            _buffLevelCfg = buffLevel;
            _active = true;
            _spellEntity.BeforeDeathEvent += Execute;
        }

        public void OnEnable()
        {

        }

        private void Execute(GameObject hitGo)
        {
            var offset = 1f;
            var hitCollider = hitGo?.GetComponent<Collider2D>();
            var health = hitGo?.GetComponent<Health>();
            var spawnPosition = transform.position;
            if (hitGo)
            {
                spawnPosition = hitGo.transform.position;
            }

            if (hitCollider && health)
            {
                offset = Math.Max(hitCollider.bounds.extents.x, hitCollider.bounds.extents.y);
                offset = Math.Max(offset, hitCollider.bounds.extents.z);
                offset *= 2;
            }

            if (_spellEntity.rangeX && _spellEntity.rangeY)
            {
                offset += _spellEntity.SpellBaseData.GetValue(SpellAttribute.Range);
            }
            else if (!hitGo && (_spellEntity.rangeX || _spellEntity.rangeY))
            {
                spawnPosition += _projectile.Direction * _spellEntity.SpellBaseData.GetValue(SpellAttribute.Range);
            }

            var buffElements = new List<BuffElement>(_spellEntity.ActiveBuffs);

            for (int i = buffElements.Count - 1; i >= 0; i--)
            {
                var buff = buffElements[i];
                if (!buff.BuffCfg.CanInherit)
                {
                    buffElements.RemoveAt(i);
                }
            }

            var spellBaseData = new SpellBaseData(_spellEntity.SpellLevel);
            var splitNum = _buffLevelCfg.GetExtraData(BuffLevel.SplitNum);
            spellBaseData.ApplyBuff(SpellAttribute.SpawnNum, BuffEffectOperator.SET, splitNum);
            spellBaseData.ApplyBuff(SpellAttribute.Damage, BuffEffectOperator.MUL, _buffLevelCfg.GetExtraData(BuffLevel.SonDamageRate));
            spellBaseData.ApplyBuff(SpellAttribute.Range, BuffEffectOperator.MUL, _buffLevelCfg.GetExtraData(BuffLevel.SonRangeRate));
            spellBaseData.ApplyBuff(SpellAttribute.ScatteringDegree, BuffEffectOperator.SET, (splitNum - 1) / splitNum * 360);
            //随机一个方向
            var direction = Random.insideUnitCircle.normalized;
            //这里分裂目前不走释放前的buff应用了，因为无需处理蓝耗，后面有其他需求再添加
            //buffElements目前不克隆，没有修改的需求，用一份就好
            BattleHelper.SpawnProjectileEntity(_spellEntity.SpellLevel, _projectile.GetOwner(), direction, _projectile.name,
                spawnPosition, spellBaseData, buffElements, _uniformScatter, offset);

            _spellEntity.BeforeDeathEvent -= Execute;
            TryUnregister();
        }

        public void TryUnregister()
        {
            if (_active)
            {
                _active = false;
                _spellEntity.BeforeDeathEvent -= Execute;
            }
        }

        public void OnDisable()
        {
            TryUnregister();
        }

        public void OnDestroy()
        {
            TryUnregister();
        }
    }
}