using Battle.Component;
using Battle.Spell;
using cfg.skill;
using MoreMountains.TopDownEngine;
using UnityEngine;

namespace Battle.Buff
{
    public class Buff20008:BuffElement
    {
        public Buff20008(int id)
        {
            ID = id;
            Type = ElementType.Buff;
        }
        
        public override void ApplyBuff(GameObject target)
        {
            var damage = target.GetComponent<DamageOnTouch>();
            var iceType = new TypedDamage();
            iceType.ForceCharacterCondition = true;
            iceType.ForcedConditionDuration = BuffLevelCfg.GetExtraData(BuffLevel.FreezeTime);
            iceType.ForcedCondition = CharacterStates.CharacterConditions.Frozen;
            damage.TypedDamages.Add(new TypedDamage());
        }
    }
}