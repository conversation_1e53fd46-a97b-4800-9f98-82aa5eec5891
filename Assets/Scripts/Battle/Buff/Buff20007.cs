using Battle.Component;
using Battle.Spell;
using cfg.skill;
using UnityEngine;

namespace Battle.Buff
{
    public class Buff20007:BuffElement
    {
        public Buff20007(int id)
        {
            ID = id;
            Type = ElementType.Buff;
        }
        
        public override void ApplyBuff(GameObject target)
        {
            var spellEntity = target.GetComponent<SpellEntity>();
            spellEntity.TargetFindType = TargetFindType.MinDirection;
            
            var projectComponent = target.GetComponent<TargetProjectTileExt>();
            projectComponent.FindTargetRange = float.MaxValue;
        }
    }
}