using Battle.Component;
using Unity.VisualScripting;
using UnityEngine;

namespace Battle.Buff
{
    /// <summary>
    /// 分裂
    /// </summary>
    public class Buff20003 : BuffElement
    {
        public Buff20003(int id)
        {
            ID = id;
            Type = ElementType.Buff;
        }
        
        public override void ApplyBuff(GameObject target)
        {
            var endSplitComponent = target.GetOrAddComponent<EndSplitComponent>();
            endSplitComponent.Set(BuffLevelCfg);
            endSplitComponent.enabled = true;
        }
    }
}