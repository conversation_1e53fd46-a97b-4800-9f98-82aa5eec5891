using cfg;
using System;
using System.Collections.Generic;
using System.Linq;
using Unity.VisualScripting;
using UnityEngine;

public class Formula
{
    public static (int, int) GetNextSection()
    {
        int section = PlayerData.GetInstance().CurSection;
        int level = PlayerData.GetInstance().CurLevel;

        level += 1;
        var scf = DataTableManager.Instance.Tables.TbSection.Get(section: section, level: level);
        if (scf != null)
        {
            return (section, level);
        }
        else
        {
            section += 1;
            level = 1;
            scf = DataTableManager.Instance.Tables.TbSection.Get(section: section, level: level);
            if (scf != null)
            {
                return (section, level);
            }
        }

        return (0, 0);
    }

    public static bool isInLevelRoom()
    {
        return PlayerData.GetInstance().LevelData.MapID > 0;
    }

    public static List<int> GetRandomElementsBasedOnWeights(Dictionary<int, int> weights, int count = 1)
    {
        Dictionary<int, int> remaining = new Dictionary<int, int>(weights);
        List<int> selectedElements = new List<int>();

        count = Mathf.Min(count, remaining.Count);

        for (int i = 0; i < count; i++)
        {
            int totalWeight = remaining.Values.Sum();

            if (totalWeight <= 0)
                break;

            int randomNumber = UnityEngine.Random.Range(0, totalWeight);
            int selectedElement = 0;

            foreach (var it in remaining)
            {
                if (randomNumber < it.Value)
                {
                    selectedElement = it.Key;
                    break;
                }
                randomNumber -= it.Value;
            }

            selectedElements.Add(selectedElement);
            remaining.Remove(selectedElement);
        }

        return selectedElements;
    }

    public static List<cfg.RandomItem> GetRandomItemBasedOnWeights(List<cfg.RandomItemGroup> groups, int count = 1)
    {
        Dictionary<int, int> groupWeights = new Dictionary<int, int>();
        for (int i = 0; i < groups.Count; i++)
        {
            groupWeights[i] = groups[i].Weight;
        }

        List<int> selectedGroupIndices = GetRandomElementsBasedOnWeights(groupWeights, count);
        List<cfg.RandomItem> selectedItems = new List<cfg.RandomItem>();

        foreach (int groupIndex in selectedGroupIndices)
        {
            if (groupIndex < groups.Count)
            {
                var group = groups[groupIndex];
                if (group.Items != null && group.Items.Count > 0)
                {
                    selectedItems.AddRange(group.Items);
                }
            }
        }

        return selectedItems;
    }

    /// <summary>
    /// 获取门的资源
    /// </summary>
    static Dictionary<int, string> DoorResMap =new Dictionary<int, string>
    {
        { 1, "Items/Doors/scene_jbd_door"},
        {2, "Items/Doors/scene_mfs_door" },
    };
    public static string GetDoorRes(int bonusType)
    {
        return DoorResMap.GetValueOrDefault(bonusType);
    }

    /// <summary>
    /// 从奖励模式表中获取随机到的ID
    /// </summary>
    /// <param name="mode"></param>
    /// <returns></returns>
    public static int GetIDFromBonusModeTable(int mode)
    {
        cfg.bonus.BonusMode bonusModeTBROW = DataTableManager.Instance.Tables.TbBonus.Get(mode);
        if (bonusModeTBROW.Mode == 1 || bonusModeTBROW.Mode == 2)
        { // 金币 or 法术
            List<int> randomBonusID = GetRandomElementsBasedOnWeights(bonusModeTBROW.RandomDropLibrary, count: 1);
            return randomBonusID[0];
        }
        return 0;
    }

    /// <summary>
    /// 独立概率: 万分比 -> 抓单个
    /// </summary>
    /// <param name="probability"></param>
    /// <returns></returns>
    public static bool GetIndependentProbabilitySingle(int probability)
    {
        int randomValue = UnityEngine.Random.Range(0, 10000);
        return randomValue < probability;
    }

    /// <summary>
    /// 独立概率: 万分比 -> 抓多个
    /// </summary>
    /// <param name="items"></param>
    /// <returns></returns>
    public static List<Tuple<int, int>> GetIndependentProbabilityMulti(List<cfg.DropGroupItem> items)
    {
        List<Tuple<int, int>> selectedItems = new List<Tuple<int, int>>();
        if (items == null || items.Count == 0)
            return selectedItems;

        foreach (var item in items)
        {
            if(GetIndependentProbabilitySingle(item.Weight))
            {
                selectedItems.Add(new Tuple<int, int>(item.ItemId, item.ItemCount));
            }
        }

        return selectedItems;
    }

    /// <summary>
    /// 道具圆桌概率
    /// </summary>
    /// <param name="items"></param>
    /// <returns></returns>
    public static List<Tuple<int, int>> GetRoundTableProbability(List<cfg.DropGroupItem> items)
    {
        List<Tuple<int, int>> selectedItems = new List<Tuple<int, int>>();
        
        if (items == null || items.Count == 0)
            return selectedItems;
        
        // 计算权重总和
        int totalWeight = 0;
        foreach (var item in items)
        {
            totalWeight += item.Weight;
        }
        
        if (totalWeight <= 0)
            return selectedItems;
        
        // 生成随机数
        int randomNumber = UnityEngine.Random.Range(0, totalWeight);
        
        // 根据权重选择物品
        foreach (var item in items)
        {
            if (randomNumber < item.Weight)
            {
                // 返回选中的物品ID和数量
                selectedItems.Add(new Tuple<int, int>(item.ItemId, item.ItemCount));
                break;
            }
            randomNumber -= item.Weight;
        }
        
        return selectedItems;
    }

    /// <summary>
    /// 根据掉落表获取随机物品
    /// </summary>
    public static List<Tuple<int, int>> GetRandomItemBasedOnDropTb(int tid, int type, int count = 1)
    {
        List<Tuple<int, int>> randomItems = new List<Tuple<int, int>>();
        cfg.drop.DropItem dropItem = DataTableManager.Instance.Tables.TbDrop.Get(tid);

        Debug.Log($"GetRandomItemBasedOnDropTb: Processing drop table ID {tid}, type {type}");

        if (type == 1)
        {
            // 采用【掉落.xlsx】中的 random_drop_item 实现
            List<cfg.RandomItem> tmpItems = GetRandomItemBasedOnWeights(dropItem.RandomDropItem, count);
            foreach (var item in tmpItems)
            {
                Debug.Log($"GetRandomItemBasedOnDropTb: Type 1 - Adding item ID {item.ItemId}, count {item.ItemCount}");
                randomItems.Add(new Tuple<int, int>(item.ItemId, item.ItemCount));
            }
        }
        else if (type == 2)
        {
            // 采用【掉落.xlsx】中的 random_drop_group 实现
            Debug.Log($"GetRandomItemBasedOnDropTb: Type 2 - Processing {dropItem.RandomDropGroup.Count} drop groups");

            for (int i = 0; i < dropItem.RandomDropGroup.Count; i++)
            {
                bool isCapture = GetIndependentProbabilitySingle(dropItem.RandomDropGroup[i].RandomGroupSetting.GroupCaptureProbability);
                Debug.Log($"GetRandomItemBasedOnDropTb: Group {i} capture probability {dropItem.RandomDropGroup[i].RandomGroupSetting.GroupCaptureProbability}, captured: {isCapture}");

                if (!isCapture)
                {
                    continue;
                }

                for (int j = 0; j < dropItem.RandomDropGroup[i].RandomGroupSetting.GroupCaptureNum; j++)
                {
                    // 组内随机算法
                    int algorithm = dropItem.RandomDropGroup[i].RandomGroupSetting.Algorithm;
                    Debug.Log($"GetRandomItemBasedOnDropTb: Group {i}, iteration {j}, algorithm {algorithm}");

                    if (algorithm == 1)
                    { // 独立概率
                        var groupItems = GetIndependentProbabilityMulti(dropItem.RandomDropGroup[i].Items);
                        foreach (var item in groupItems)
                        {
                            Debug.Log($"GetRandomItemBasedOnDropTb: Algorithm 1 - Adding item ID {item.Item1}, count {item.Item2}");
                        }
                        randomItems.AddRange(groupItems);
                    }
                    else if (algorithm == 2)
                    { // 圆桌概率
                        var groupItems = GetRoundTableProbability(dropItem.RandomDropGroup[i].Items);
                        foreach (var item in groupItems)
                        {
                            Debug.Log($"GetRandomItemBasedOnDropTb: Algorithm 2 - Adding item ID {item.Item1}, count {item.Item2}");
                        }
                        randomItems.AddRange(groupItems);
                    }
                }
            }

        }

        Debug.Log($"GetRandomItemBasedOnDropTb: Final result - {randomItems.Count} items generated");
        return randomItems;
    }
}