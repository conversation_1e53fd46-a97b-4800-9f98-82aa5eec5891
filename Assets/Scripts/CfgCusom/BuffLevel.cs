using System.Collections.Generic;

namespace cfg.skill
{
    public partial class BuffLevel
    {
        // todo这里改成枚举好一点
        public const string SonDamageRate = "son_damage_rate";
        public const string SonRangeRate = "son_range_rate";
        public const string SplitNum = "split_num";
        public const string ReduceSpeedRate = "reduce_speed_rate";
        public const string FreezeTime = "freeze_time";
        public const string ReduceFlyObjRate = "reduce_fly_obj_rate";

        public float GetExtraData(string key, float defaultValue = 0)
        {
            return ExtraData.GetValueOrDefault(key, defaultValue);
        }
    }
}