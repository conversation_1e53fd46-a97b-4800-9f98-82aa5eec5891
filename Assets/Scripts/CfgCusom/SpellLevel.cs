using System.Collections.Generic;

namespace cfg.skill
{
    public partial class SpellLevel
    {
        // todo这里改成枚举好一点
        public const string TraceSpeedRate = "trace_speed_rate";
        public const string AttractRadius = "attract_radius";
        public const string SplitTime = "split_time";
        public const string BeginTraceTime = "begin_trace_time";

        public float GetExtraData(string key, float defaultValue = 0)
        {
            return ExtraData.GetValueOrDefault(key, defaultValue);
        }
    }
}