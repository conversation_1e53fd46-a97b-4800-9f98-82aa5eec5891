using System.Collections.Generic;

public class LevelData
{
    public int m_MapID = 0; // 地图ID
    public int m_CurMonsterRefreshIndex = 1; // 当前怪物刷新波次
    public int m_BonusMode = 0; // 奖励模式
    public List<int> m_RandomBonusModeList = new List<int>(); // 当前关卡的奖励模式

    public int MapID
    {
        get { return m_MapID; }
        set { m_MapID = value; }
    }

    public int CurMonsterRefreshIndex
    {
        get { return m_CurMonsterRefreshIndex; }
        set { m_CurMonsterRefreshIndex = value; }
    }

    public List<int> RandomBonusModeList
    {
        get { return m_RandomBonusModeList; }
        set { m_RandomBonusModeList = value; }
    }

    public int BonusMode
    {
        get { return m_BonusMode; }
        set { m_BonusMode = value; }
    }
}