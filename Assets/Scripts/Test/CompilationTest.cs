using UnityEngine;

namespace Test
{
    /// <summary>
    /// Simple test to verify all fixes are working
    /// </summary>
    public class CompilationTest : MonoBehaviour
    {
        [Head<PERSON>("Test Settings")]
        public bool runTestOnStart = false;

        void Start()
        {
            if (runTestOnStart)
            {
                TestAllDataTableAccess();
            }
        }

        [ContextMenu("Test All Data Table Access")]
        public void TestAllDataTableAccess()
        {
            Debug.Log("=== Testing All Data Table Access ===");

            if (DataTableManager.Instance == null)
            {
                Debug.LogError("DataTableManager.Instance is null");
                return;
            }

            // Test TbSection (uses Get method, already safe)
            TestTbSection();

            // Test TbMap (uses GetOrDefault)
            TestTbMap();

            // Test TbBonus (uses GetOrDefault)
            TestTbBonus();

            // Test TbMonster (uses GetOrDefault)
            TestTbMonster();

            // Test TbDrop (uses GetOrDefault)
            TestTbDrop();

            // Test TbItems (uses GetOrDefault)
            TestTbItems();

            Debug.Log("=== All Data Table Tests Completed ===");
        }

        private void TestTbSection()
        {
            Debug.Log("--- Testing TbSection ---");
            
            // Test valid section
            var section1 = DataTableManager.Instance.Tables.TbSection.Get(section: 1, level: 1);
            if (section1 != null)
            {
                Debug.Log("✓ TbSection.Get(1, 1) works");
            }
            else
            {
                Debug.LogWarning("⚠ TbSection.Get(1, 1) returned null");
            }

            // Test invalid section
            var section2 = DataTableManager.Instance.Tables.TbSection.Get(section: 999, level: 999);
            if (section2 == null)
            {
                Debug.Log("✓ TbSection.Get(999, 999) safely returned null");
            }
            else
            {
                Debug.LogWarning("⚠ TbSection.Get(999, 999) unexpectedly found data");
            }
        }

        private void TestTbMap()
        {
            Debug.Log("--- Testing TbMap ---");
            
            var map1 = DataTableManager.Instance.Tables.TbMap.GetOrDefault(1);
            if (map1 != null)
            {
                Debug.Log("✓ TbMap.GetOrDefault(1) works");
            }
            else
            {
                Debug.LogWarning("⚠ TbMap.GetOrDefault(1) returned null");
            }

            var map2 = DataTableManager.Instance.Tables.TbMap.GetOrDefault(999999);
            if (map2 == null)
            {
                Debug.Log("✓ TbMap.GetOrDefault(999999) safely returned null");
            }
        }

        private void TestTbBonus()
        {
            Debug.Log("--- Testing TbBonus ---");
            
            var bonus1 = DataTableManager.Instance.Tables.TbBonus.GetOrDefault(10001);
            if (bonus1 != null)
            {
                Debug.Log("✓ TbBonus.GetOrDefault(10001) works");
            }
            else
            {
                Debug.LogWarning("⚠ TbBonus.GetOrDefault(10001) returned null");
            }

            var bonus2 = DataTableManager.Instance.Tables.TbBonus.GetOrDefault(999999);
            if (bonus2 == null)
            {
                Debug.Log("✓ TbBonus.GetOrDefault(999999) safely returned null");
            }
        }

        private void TestTbMonster()
        {
            Debug.Log("--- Testing TbMonster ---");
            
            var monster1 = DataTableManager.Instance.Tables.TbMonster.GetOrDefault(10001);
            if (monster1 != null)
            {
                Debug.Log("✓ TbMonster.GetOrDefault(10001) works");
            }
            else
            {
                Debug.LogWarning("⚠ TbMonster.GetOrDefault(10001) returned null");
            }

            var monster2 = DataTableManager.Instance.Tables.TbMonster.GetOrDefault(999999);
            if (monster2 == null)
            {
                Debug.Log("✓ TbMonster.GetOrDefault(999999) safely returned null");
            }
        }

        private void TestTbDrop()
        {
            Debug.Log("--- Testing TbDrop ---");
            
            var drop1 = DataTableManager.Instance.Tables.TbDrop.GetOrDefault(10001);
            if (drop1 != null)
            {
                Debug.Log("✓ TbDrop.GetOrDefault(10001) works");
            }
            else
            {
                Debug.LogWarning("⚠ TbDrop.GetOrDefault(10001) returned null");
            }

            var drop2 = DataTableManager.Instance.Tables.TbDrop.GetOrDefault(999999);
            if (drop2 == null)
            {
                Debug.Log("✓ TbDrop.GetOrDefault(999999) safely returned null");
            }
        }

        private void TestTbItems()
        {
            Debug.Log("--- Testing TbItems ---");
            
            var item1 = DataTableManager.Instance.Tables.TbItems.GetOrDefault(10001);
            if (item1 != null)
            {
                Debug.Log("✓ TbItems.GetOrDefault(10001) works");
            }
            else
            {
                Debug.LogWarning("⚠ TbItems.GetOrDefault(10001) returned null");
            }

            var item2 = DataTableManager.Instance.Tables.TbItems.GetOrDefault(999999);
            if (item2 == null)
            {
                Debug.Log("✓ TbItems.GetOrDefault(999999) safely returned null");
            }
        }

        [ContextMenu("Test SIXGameManager Safety")]
        public void TestSIXGameManagerSafety()
        {
            Debug.Log("=== Testing SIXGameManager Safety ===");

            if (SIXGameManager.Instance == null)
            {
                Debug.LogWarning("SIXGameManager.Instance is null - this is expected if not in game scene");
                return;
            }

            Debug.Log("✓ SIXGameManager.Instance exists and is accessible");

            // Test if we can access PlayerData safely
            if (PlayerData.GetInstance() != null)
            {
                Debug.Log("✓ PlayerData.GetInstance() is accessible");
                
                if (PlayerData.GetInstance().LevelData != null)
                {
                    Debug.Log("✓ PlayerData.GetInstance().LevelData is accessible");
                }
                else
                {
                    Debug.LogWarning("⚠ PlayerData.GetInstance().LevelData is null");
                }
            }
            else
            {
                Debug.LogWarning("⚠ PlayerData.GetInstance() is null");
            }
        }
    }
}
