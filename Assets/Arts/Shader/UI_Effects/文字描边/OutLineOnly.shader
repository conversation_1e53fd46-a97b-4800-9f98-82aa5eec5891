Shader "TextOutLine/OutLineOnly"
{
    Properties
    {
        [PerRendererData] _MainTex ("Main Texture", 2D) = "white" {}
        _Color ("Tint", Color) = (1, 1, 1, 1)

        [PerRendererData]_OutlineColor ("Outline Color", Color) = (1, 1, 1, 1)
        [PerRendererData]_OutlineWidth ("Outline Width", Range(0,5)) = 1
 
        _StencilComp ("Stencil Comparison", Float) = 8
        _Stencil ("Stencil ID", Float) = 0
        _StencilOp ("Stencil Operation", Float) = 0
        _StencilWriteMask ("Stencil Write Mask", Float) = 255
        _StencilReadMask ("Stencil Read Mask", Float) = 255
 
        _ColorMask ("Color Mask", Float) = 15
 
        [Toggle(UNITY_UI_ALPHACLIP)] _UseUIAlphaClip ("Use Alpha Clip", Float) = 0

    }
    SubShader
    {
         Tags
        { 
            "Queue"="Transparent" 
            "IgnoreProjector"="True" 
            "RenderType"="Transparent" 
            "PreviewType"="Plane"
            "CanUseSpriteAtlas"="True"
        }
        
        Stencil
        {
            Ref [_Stencil]
            Comp [_StencilComp]
            Pass [_StencilOp] 
            ReadMask [_StencilReadMask]
            WriteMask [_StencilWriteMask]
        }
 
        Cull Off
        Lighting Off
        ZWrite Off
        ZTest [unity_GUIZTestMode]
        Blend SrcAlpha OneMinusSrcAlpha
        ColorMask [_ColorMask]
 
        Pass
        {
            Name "OUTLINE"
 
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma target 2.0

            //for RectMask2D  
			#include "UnityUI.cginc"  
            #include "UnityCG.cginc" 

            struct a2v
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
                float2 uv1 : TEXCOORD1;
                float2 uv2 : TEXCOORD2;
                float2 uv3 : TEXCOORD3;
                float4 tangent : TANGENT;
                float4 normal : NORMAL;
                fixed4 color :COLOR;
            };

            struct v2f
            {
                float4 pos : SV_POSITION;
                float2 uv : TEXCOORD0;
                float2 uv1 : TEXCOORD1;
                float2 uv2 : TEXCOORD2;
                float2 uv3 : TEXCOORD3;
                float4 tangent : TANGENT;
                float4 normal : NORMAL;
                float4 worldPos : TEXCOORD4;
                fixed4 color :COLOR;
            };

            sampler2D _MainTex;
            float4 _MainTex_TexelSize;

            fixed4 _Color;

            fixed4 _TextureSampleAdd;

            //for RectMask2D  
            float4 _ClipRect;

            fixed IsInRect(float2 pPos, float2 pClipRectMin, float2 pClipRectMax)
            {
                pPos = step(pClipRectMin, pPos) * step(pPos, pClipRectMax);
                return pPos.x * pPos.y;
            }

            fixed SampleAlpha(int pIndex, v2f IN)
            {
                const fixed sinArray[12] = {0, 0.5, 0.87, 1, 0.87, 0.5, 0, -0.5, -0.87, -1, -0.87, -0.5};
                const fixed cosArray[12] = {1, 0.87, 0.5, 0, -0.5, -0.87, -1, -0.87, -0.5, 0, 0.5, 0.87};
                float2 pos = IN.uv +  _MainTex_TexelSize.xy * float2(cosArray[pIndex], sinArray[pIndex]) * IN.uv3.x ;
                //return IsInRect(pos, IN.uv1, IN.uv2) * (tex2D(_MainTex, pos) + _TextureSampleAdd).a * _OutlineColor.a;
                return IsInRect(pos, IN.uv1, IN.uv2) * (tex2D(_MainTex, pos) + _TextureSampleAdd).a * IN.normal.w;
            }

            v2f vert (a2v v)
            {
                v2f o;
                o.pos = UnityObjectToClipPos(v.vertex);
                o.uv = v.uv;
                o.uv1 = v.uv1;
                o.uv2 = v.uv2;
                o.uv3 = v.uv3;
                o.tangent = v.tangent;
                o.normal = v.normal;
                o.worldPos = v.vertex;
                o.color = v.color;

                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {
    
                fixed4 col = (tex2D(_MainTex, i.uv) + _TextureSampleAdd) * i.color;


                if(i.uv3.x > 0)
                {
                    col.a *= IsInRect(i.uv, i.uv1, i.uv2);    //默认文字颜色

                    //fixed4 val = fixed4(_OutlineColor.rgb, 0);   //判断是否在描边范围
                    fixed4 val = fixed4(i.tangent.z, i.tangent.w, i.normal.z, 0);

                    val.a += SampleAlpha(0, i);
                    val.a += SampleAlpha(1, i);
                    val.a += SampleAlpha(11, i);
                    val.a += SampleAlpha(2, i);
                    val.a += SampleAlpha(3, i);
                    val.a += SampleAlpha(4, i);
                    val.a += SampleAlpha(5, i);
                    val.a += SampleAlpha(6, i);
                    val.a += SampleAlpha(7, i);
                    val.a += SampleAlpha(8, i);
                    val.a += SampleAlpha(9, i);
                    val.a += SampleAlpha(10, i);
                    
                    val.a = saturate(val.a);

                    fixed outlinealpha = saturate(i.uv3.y - smoothstep(0.001, 0.8, col.a));
                    col = (val * outlinealpha) + (col * col.a);
                    //col.a = saturate(col.a);
                    //col.a *= i.color.a;
                }

                col.a *= UnityGet2DClipping(i.worldPos.xy, _ClipRect);
                #ifdef UNITY_UI_ALPHACLIP
                    clip(col.a - 0.001);
                #endif
                
                return col;
            }
            ENDCG
        }
    }
}
