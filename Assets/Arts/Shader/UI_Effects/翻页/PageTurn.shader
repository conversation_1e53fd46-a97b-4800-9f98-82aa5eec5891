Shader "Unlit/PageTurn"
{
    Properties
    {
        _MainTex ("MainTex", 2D) = "white" {}
        _BackTex ("BackTex", 2D) = "white" {}
        _MaskTex ("MaskTex", 2D) = "white" {}
        _Progress ("Progress", float) = 5
        _TilingAndOffset ("Tiling And Offset", vector) = (1.5, 2.0, 0, 0)
        _Direction ("Direction(XYZ)", vector) = (1.3, 2.6, -0.95, 0)
        _CurveShadow ("Curve Shadow Power", range(0.001, 1)) = 0
        //_FrontShadow ("Front Shadow Power", range(0, 1)) = 0
        _ShadowColor ("Shadow Color", Color) = (0, 0, 0, 0)
    }
    SubShader
    {
        Tags 
        { 
            "RenderType" = "Transparent" 
            "Queue" = "Transparent" 
            "IgnoreProjector" = "True" 
            "PreviewType" = "Plane" 
        }
        
        Blend SrcAlpha OneMinusSrcAlpha

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #include "UnityCG.cginc"

            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
            };

            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
            };

            sampler2D _MainTex;
            sampler2D _BackTex;
            sampler2D _MaskTex;
            float _Progress;
            float4 _MainTex_ST;
            float4 _TilingAndOffset;
            float4 _Direction;
            float _CurveShadow;
            float _FrontShadow;
            float4 _ShadowColor;

            v2f vert (appdata v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);
                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {
                fixed2 center = fixed2(_TilingAndOffset.x * -1.0, _TilingAndOffset.y) * fixed2(0.5, 0.5) - fixed2(0.5, 0.5);
                fixed2 uv = i.uv * fixed2(_TilingAndOffset.x * -1.0, _TilingAndOffset.y) - center - fixed2(_TilingAndOffset.z, _TilingAndOffset.w);
                fixed2 screen = uv * fixed2(1024, 1024);
                fixed2 aspect = fixed2(_Direction.x, 1.0) / fixed2(1024, 1024);
                uv = screen * aspect;
                
                fixed2 mouse = fixed2(_Direction.y, _Direction.z);
                fixed2 angle = aspect * mouse;
                fixed2 direction = normalize(mouse + fixed2(0.001, 0.001));
                fixed2 dir = angle - direction * angle.x / direction.x;
                fixed dir_dot = dot(uv - dir, direction);

                fixed dis = distance(angle - dir, fixed2(0, 0)) + (_Direction.x - _Progress * 0.1 * _Direction.x / direction.x);
                fixed clamp_dis = clamp(dis, 0.0, _Direction.x / direction.x);

                dis = direction.x == 0.0 ? clamp_dis : dis;
                
                //区分翻折过的区域
                fixed curvebackarea = dir_dot - dis;
                fixed2 aspect_div = fixed2(1.0 / _Direction.x, 1.0);
                fixed split = asin(curvebackarea / 0.1);

                fixed2 s_d = uv - direction * curvebackarea;
                fixed2 curve = (3.142 - split) * direction * 0.1 + s_d;

                fixed curve_edge = 1.0 - step(_Direction.x, curve.y);
                fixed curve_up = curve_edge > 1.0 ? curve_edge : 1.0 - step(1.0, curve.y);
                fixed curve_front = curve_up > 1.0 ? curve_up : curve_up <= 1.0 && curve_up >= 1.0 ? step(0.0, curve.x) : curve_up;
                fixed curve_down = curve_front > 1.0 ? curve_front : curve_front <= 1.0 && curve_front >= 1.0 ? step(0.0, curve.y) : curve_front;

                //翻折弯曲部分UV
                fixed2 curve_soft = s_d + split * direction * fixed2(0.1, 0.1);
                fixed2 curve_uv = aspect_div * fixed2(curve_down > 1.0 ? curve_soft.x : curve_down <= 1.0 && curve_down >= 1.0 ? curve.x : curve_soft.x,
                                curve_down > 1.0 ? curve_soft.y : curve_down - 0.0 <= 1.0 && curve_down + 0.0 >= 1.0 ? curve.y : curve_soft.y);
                curve_uv = fixed2(1.0 - curve_uv.x, curve_uv.y);
                
                fixed4 curve_col = tex2D(_MainTex, curve_uv);
                fixed4 mask_col = tex2D(_MaskTex, curve_uv);

                //mask
                fixed curve_mask = curve_col.a * (curve_down * mask_col.a) + (mask_col.a - curve_down);
                
                //翻页裁剪掉多余部分
                fixed rest = curve_down * ((1.0 - curve_mask) * step(0.0, curve.y));
                fixed shadow_area = (1.0 - rest) * curve_mask;
                fixed shadow = clamp((0.1 - abs(curvebackarea)) / 0.1, 0.0, 1.0);

                fixed pow_shadow = pow(shadow, _CurveShadow * 2);
                fixed shadow_rest = pow_shadow * rest;

                fixed curve_area = curve_down * (shadow_area - shadow_rest) * curve_front;

                fixed2 shadow_uv = aspect_div * fixed2(curve_area > 1.0 ? curve_soft.x : curve_area <= 1.0 && curve_area >= 1.0 ? curve.x : curve_soft.x,
                                    curve_area > 1.0 ? curve_soft.y : curve_area <= 1.0 && curve_area >= 1.0 ? curve.y : curve_soft.y);
                shadow_uv = fixed2(1.0 - shadow_uv.x, shadow_uv.y);

                //剔除卷页下面部分，显示正面图片
                //卷页部分显示背面图片
                fixed4 shadow_col = lerp(tex2D(_BackTex, shadow_uv), tex2D(_MainTex, shadow_uv), shadow_rest + shadow_area - curve_area - 1 + curve_mask);
                shadow_col = fixed4(lerp(_ShadowColor.rgb, shadow_col.rgb, pow_shadow), step(curvebackarea, 0.0989) * shadow_col.a);
                //return half4(( shadow_rest + shadow_area - curve_area - 1 + curve_mask).xxx, 1);
                fixed2 fanzhuan = s_d + direction * (abs(curvebackarea) + 0.3142);

                fixed curvearea = 0;
                if( fanzhuan.x <= _Direction.x && fanzhuan.y < 1)
                    curvearea = 1.0;
                else
                    curvearea = 0.0;

                fixed curvearea_mask = 0;
                if( curvearea == 1.0 )
                    curvearea_mask = step(0.0, fanzhuan.y);
                else
                    curvearea_mask = curvearea;
                
                fixed curvearea_u = 0;
                fixed curvearea_v = 0;
                if( curvearea_mask == 1.0 )
                {
                    curvearea_u = fanzhuan.x;
                    curvearea_v = fanzhuan.y;
                }
                else
                {
                    curvearea_u = uv.x;
                    curvearea_v = uv.y;
                }
                
                fixed2 gv = fixed2(curvearea_u, curvearea_v) * aspect_div;
                gv = fixed2( 1.0 - gv.x, gv.y);
                
                fixed4 back_col = tex2D(_BackTex, gv);
                back_col *= back_col.a;
                
                fixed2 gvv = uv * aspect_div;
                
                //边缘阴影区域
                fixed edgeshadow = tex2D(_MaskTex, gvv).x * ((1.0 - step(1.0, tex2D(_MaskTex, gv).x)) * step(curvebackarea, 0.0));
                float4 col_front = tex2D(_MainTex, half2(1 - gvv.x, gvv.y));
                fixed4 col = lerp(col_front, back_col, curvearea_mask * back_col.a);
                
                fixed4 finalcol = col_front;
                if( curvebackarea >= 0.0)
                    finalcol = shadow_col;
                else if( curvebackarea < 0.0)
                    finalcol = col ;
                
                
                float alpha = smoothstep(0.9, 1, finalcol.a);
                return half4(finalcol.rgb, alpha);
            }
            ENDCG
        }
    }
}
