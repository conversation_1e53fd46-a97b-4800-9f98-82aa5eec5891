Shader "UI/NineGrid"
{
    Properties
    {
        _MainTex ("MainTex", 2D) = "white" {}
        _MaskTex ("MaskTex", 2D) = "white" {}
        _Color ("BG Color", color) = (1,1,1,1)
        
        [Space(20)]
        _ImageSizeX ("ImageSize X", float) = 1
        _ImageSizeY ("ImageSize Y", float) = 1
        _Size ("Size", float) = 5
        _PositionX ("Position X", float) = 0.5
        _PositionY ("Position X", float) = 0.5
        _ScaleX ("Scale X", float) = 1
        _ScaleY ("Scale X", float) = 1
        
        [Space(20)]
        _LineX_1 ("LineX 1", range(0, 1)) = 0.1
        _LineX_2 ("LineX 2", range(0, 1)) = 0.9
        _LineY_1 ("LineY 1", range(0, 1)) = 0.1
        _LineY_2 ("LineY 2", range(0, 1)) = 0.9
    }
    SubShader
    {
        Pass
        {
            Tags 
            { 
                "RenderType" = "Transparent" 
                "Queue" = "Transparent"
                "PreviewType" = "Plane"
            }
            Blend SrcAlpha OneMinusSrcAlpha
            
            CGPROGRAM
            
            #pragma vertex vert
            #pragma fragment frag
    
            #include "UnityCG.cginc"

            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
            };

            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
            };

            sampler2D _MainTex, _MaskTex;
            float4 _MainTex_ST;
            float4 _Color;

            float _ImageSizeX, _ImageSizeY, _Size, _PositionX, _PositionY, _ScaleX, _ScaleY;
            float _LineX_1, _LineX_2, _LineY_1, _LineY_2;
            
            v2f vert (appdata v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);
                return o;
            }

            float nine_grid(float uv, float line1, float line2, float scale)
            {
                float sxl = step(line1, uv);
                float sxr = step(line2, uv);

                float lx = uv * scale * (1 - sxl) + sxl * max(line1, uv);
                float rx = (uv * scale - (scale -1)) * sxr + (1 - sxr) * min(line2, uv);

                if (uv < line1)
                {
                    uv = uv > line1 / scale ? line1 : lx;
                }
                else if(uv > line2)
                {
                    uv = uv < (1 - (1 - line2) / scale) ? line2 : rx;
                }

                return  uv;
            }

            float scaleCenter(float2 uv)
            {
                float x = max(step(-0.01, uv.x), step(uv.x, 1.01));
                float y = max(step(-0.01, uv.y), step(uv.y, 1.01));
                float alpha = max(x, y);
                return alpha;
            }
            
            fixed4 frag (v2f i) : SV_Target
            {
                float2 uv = i.uv;
                float2 scaleUV = float2(0,0);
                float2 gridUV = float2(0,0);
                float2 centerPos = float2(_PositionX, _PositionY);
                float2 gv = (uv - centerPos) * float2(_ScaleX, _ScaleY) + centerPos;
                float albedo_area = scaleCenter(gv);
                scaleUV.xy = gv * albedo_area;

                float x_y = _ImageSizeX / _ImageSizeY;
                gridUV.x = nine_grid(scaleUV.x, _LineX_1, _LineX_2, 1/_ScaleX * _Size * x_y);
                gridUV.y = nine_grid(scaleUV.y, _LineY_1, _LineY_2, 1/_ScaleY * _Size);

                half4 albedo = tex2D(_MainTex, gridUV);

                return albedo;
            }
            ENDCG
        }
    }
}
