Shader "Unlit/Fire"
{
    Properties
    {
        _MainTex ("MainTex", 2D) = "white" {}
        _BgTex ("Background", 2D) = "white" {}
        _NoiseTex ("NoiseTex", 2D) = "white" {}
        _LineTex ("LineTes", 2D) = "white" {}

        [Space(20)]
        _WaveSpeed ("Wave Speed", float) = 0.0
        _WaveWeight ("Wave Weight", float) = 0.0

        [Space(20)]
        _LineSpeed ("Line Speed", float) = 0.0
        _LineWidth ("Line Width", float) = 0.0
        _LineSpace ("Line Space", float) = 0.0
        _LineAlpha ("Line Alpha", range(0.0, 1.0)) = 0.0

        [Space(20)]
        _SparksMovespeed ("Sparks Move speed", float) = 0.0
        _SparksWaveSpeed ("Sparks Wave Speed", float) = 0.0
        _SparksSize ("Sparks Size", float) = 0.0
        _SparksAmount ("Sparks Amount", float) = 0.0
        _SparksColor ("Sparks Color", Color) = (0.0, 0.0, 0.0, 1.0)
        _BloomColor ("Bloom Color", Color) = (0.0, 0.0, 0.0, 1.0)
    }

    SubShader
    {
        Tags{
            "Queue"="Transparent" 
            "IgnoreProjector"="True" 
            "RenderType"="Transparent" 
        }

        Cull Off
        Lighting Off
        ZWrite Off
        Blend Off

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #include "UnityCG.cginc"

            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
            };

            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
            };

            sampler2D _MainTex;
            float4 _MainTex_ST;
            sampler2D _BgTex;
            sampler2D _NoiseTex;
            sampler2D _LineTex;
            
            float _WaveSpeed;
            float _WaveWeight;
            
            float _LineSpeed;
            float _LineWidth;
            float _LineSpace;
            float _LineAlpha;

            float _SparksMovespeed;
            float _SparksWaveSpeed;
            float _SparksSize;
            float _SparksAmount;
            float4 _SparksColor;
            float4 _BloomColor;
            
            float hash1_2(float2 x)
            {
                return frac( sin( dot(x, float2(52.127, 61.2871))) * 521.582);   
            }

            float2 hash2_2(float2 x)
            {
                return frac( sin( mul(x,float2x2(20.52, 24.1994, 70.291, 80.171))) * 492.194);
            }

            //Simple interpolated noise
            float2 noise2_2(float2 uv)
            {
                //float2 f = frac(uv);
                float2 f = smoothstep(0.0, 1.0, frac(uv));
                
                float2 uv00 = floor(uv);
                float2 uv01 = uv00 + float2(0,1);
                float2 uv10 = uv00 + float2(1,0);
                float2 uv11 = uv00 + 1.0;
                float2 v00 = hash2_2(uv00);
                float2 v01 = hash2_2(uv01);
                float2 v10 = hash2_2(uv10);
                float2 v11 = hash2_2(uv11);
                
                float2 v0 = lerp(v00, v01, f.y);
                float2 v1 = lerp(v10, v11, f.y);
                float2 v = lerp(v0, v1, f.x);
                
                return v;
            }

            #define MOVEMENT_DIRECTION float2(0.8, -2.5)

            #define PARTICLE_SCALE (float2(0.5, 1.6))
            #define PARTICLE_SCALE_VAR (float2(0.25, 0.2))

            #define PARTICLE_BLOOM_SCALE (float2(0.5, 0.8))
            #define PARTICLE_BLOOM_SCALE_VAR (float2(0.3, 0.1))

            #define SPARK_COLOR float3(1.0, 0.4, 0.05) * 1.5
            #define BLOOM_COLOR float3(1.0, 0.4, 0.05) * 0.8

            #define LAYERS_COUNT 5

            //Rotates point around 0,0
            float2 rotate(float2 p, float deg)
            {
                float s = sin(deg);
                float c = cos(deg);
                return mul(float2x2(s, c, -c, s), p);
            }

            //Cell center from point on the grid
            float2 voronoiPointFromRoot(float2 root, float deg)
            {
                float2 p = hash2_2(root) - 0.5;
                float s = sin(deg);
                float c = cos(deg);
                p = mul(float2x2(s, c, -c, s), p);
                p *= 0.66;
                p += root + 0.5;
                return p;
            }

            //Voronoi cell point rotation degrees
            float degFromRootUV(float2 uv)
            {
                return _Time.y * _SparksWaveSpeed * (hash1_2(uv) - 0.5) * 2.0;   
            }

            float2 randomAround2_2(float2 p, float2 range, float2 uv)
            {
                return p + (hash2_2(uv) + 0.25) * range;
            }


            float3 fireParticles(float2 uv, float2 originalUV)
            {
                float3 f_particles = float3(0.0,0.0,0.0);

                uv.y *= 1.0;
                uv.x *= 2.5;

                float2 rootUV = floor(uv);
                
                float deg = degFromRootUV(rootUV);
                float2 pointUV = voronoiPointFromRoot(rootUV, deg);
                float dist = 0.0;
                float distBloom = 0.0;
            
                //UV manipulation for the faster particle movement
                float2 tempUV = uv + (noise2_2(uv * 2.0) ) * 0.1;
                tempUV += (noise2_2(uv * 3.0 + _Time.y)) * 0.07;

                //Sparks sdf
                dist = length(rotate(tempUV - pointUV, 0.7) * randomAround2_2(float2(0.5, 1.6), float2(0.25, 0.2), rootUV));
                
                //Bloom sdf
                distBloom = length(rotate(tempUV - pointUV, 0.7) * randomAround2_2(float2(0.5, 0.8), float2(0.3, 0.1), rootUV));

                //Add sparks
                f_particles += (1.0 - smoothstep(_SparksSize * 0.6 * 0.01, _SparksSize * 2.0 * 0.01, dist)) * _SparksColor;
                
                //Add bloom
                f_particles += pow((1.0 - smoothstep(0.0, _SparksSize * 10.0 * 0.01, distBloom)) * 1.0, 3.0) * _BloomColor;

                //Upper disappear curve randomization
                float border = (hash1_2(rootUV) - 0.5) * 2.0;
                float disappear = 1.8 - smoothstep(border, border + 0.5, originalUV.y);
                
                //Lower appear curve randomization
                border = (hash1_2(rootUV + 0.214) - 1.8) * 0.7;
                float appear = smoothstep(border, border + 0.4, originalUV.y);
                
                return f_particles * disappear * appear;
            }

            v2f vert (appdata v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);
                return o;
            }


            float4 frag (v2f i) : SV_Target
            {
                float2 uv = i.uv;

                float4 wave_col = float4(0.0, 0.0, 0.0, 0.0);
                float4 bg_col   = float4(0.0, 0.0, 0.0, 0.0);
                float4 line_col = float4(0.0, 0.0, 0.0, 0.0);
                float4 fire_col = float4(0.0, 0.0, 0.0, 0.0);
                float4 color = float4(0.0, 0.0, 0.0, 0.0);

                //wave  
                float2 noise_uv = uv * float2(4.0, 0.5) + _Time.y * _WaveSpeed;
                float noise = tex2D(_NoiseTex, noise_uv).r;
                float2 wave_uv = smoothstep(0.0, 2.0, noise) * 0.1 * _WaveWeight + uv;
                wave_col = tex2D(_MainTex, wave_uv); 
                wave_col.rgb = wave_col.rgb * wave_col.a;

                //bg
                bg_col = tex2D(_BgTex, uv); 
	            bg_col.rgb = bg_col.rgb * (1.0 - wave_col.a);

                //goldline
                line_col = tex2D(_LineTex, uv);
                float alpha = (uv.x - _Time.y * _LineSpeed) * (10.0 - _LineSpace) * 3.14;
                alpha = pow(sin(alpha), (20.0 - _LineWidth)) * 2.0;
                alpha = max(line_col.a, alpha);
                line_col *= alpha; 
                line_col *= _LineAlpha;

               //fire sparks
                float vignette = 1.0 - smoothstep(0.4, 1.4, length(uv + float2(0.0, 0.3)));
                float2 offset = float2(0.0,0.0);
                float2 noiseOffset = float2(0.0,0.0);
                float2 bokehUV = float2(0.0,0.0);

                for(int i=0; i<5; i++)
                {
                    noiseOffset = (noise2_2(uv * 1.0 * 2.0 + 0.5) - 0.5) * 0.15;

                    bokehUV = (uv * _SparksAmount + _Time * float2(-3.0, 0.8) * _SparksMovespeed) + noiseOffset; 

                    fire_col.rgb += fireParticles(bokehUV, uv);
                }

                fire_col.rgb = fire_col.rgb  + _SparksColor * 1.5 * 0.02;
                fire_col *= vignette;
                fire_col = smoothstep(0.0, 1.0, fire_col);
                fire_col.a = fire_col.r;

                color = wave_col + bg_col + fire_col;

                return color;
            }

            ENDCG
        }
    }
}
