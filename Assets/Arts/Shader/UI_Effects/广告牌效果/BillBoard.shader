Shader "Effect/BillBoard"
{
    Properties
    {
        _MainTex ("Texture", 2D) = "white" {}
    }
    SubShader
    {
        Pass
        {
            Tags 
            { 
                "RenderType" = "Transparent"
                "Queue" = "Transparent"  
                "IgnoreProjector" = "True"
                "PreviewType" = "Plane"
            }
            
            Blend SrcAlpha OneMinusSrcAlpha
            Zwrite Off
            Cull Off

            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            
            #include "UnityCG.cginc"

            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
            };

            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
            };

            sampler2D _MainTex;
            float4 _MainTex_ST;

            v2f vert (appdata v)
            {
                v2f o;
                float3 center = float3(0,0,0);
                float3 view = mul(unity_WorldToObject, float4(_WorldSpaceCameraPos, 1.0));
                float3 normalDir = normalize(view - center);    //视角 = 摄像机坐标-物体坐标
                
                float3 upDir = abs(normalDir.y) > 0.999 ? float3(0,0,1) : float3(0,1,0);
                float3 rightDir = normalize(cross(upDir, normalDir));
                upDir = normalize(cross(normalDir, rightDir));

                float3 center0ffs = v.vertex.xyz - center;    //计算中心点偏移
                float3 localPos = center + rightDir * center0ffs.x + upDir * center0ffs.y + normalDir * center0ffs.z;
                o.vertex = UnityObjectToClipPos(localPos);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);
                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {
                fixed4 col = tex2D(_MainTex, i.uv);
                return col;
            }
            ENDCG
        }
    }
}
