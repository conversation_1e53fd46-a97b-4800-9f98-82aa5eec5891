// Made with Amplify Shader Editor
// Available at the Unity Asset Store - http://u3d.as/y3X 
Shader "brushpen"
{
	Properties
	{
		_iMouse("iMouse", Vector) = (0,0,0,0)
		_MainTex("MainTex", 2D) = "white" {}
		[Toggle(_KEYWORD0_ON)] _Keyword0("Keyword 0", Float) = 0
		_Float1("Float 1", Range( 0.7 , 1)) = 0.9
		_Float2("Float 2", Float) = 0.9
	}
	
	SubShader
	{
		Tags { "RenderType"="Opaque" }
		LOD 100
		CGINCLUDE
		#pragma target 3.0
		ENDCG
		Blend Off
		Cull Back
		ColorMask RGBA
		ZWrite On
		ZTest LEqual
		Offset 0 , 0
		
		

		Pass
		{
			Name "Unlit"
			CGPROGRAM
			#pragma vertex vert
			#pragma fragment frag
			#pragma multi_compile_instancing
			#include "UnityCG.cginc"
			#include "UnityShaderVariables.cginc"
			#pragma shader_feature _KEYWORD0_ON


			struct appdata
			{
				float4 vertex : POSITION;
				UNITY_VERTEX_INPUT_INSTANCE_ID
				
			};
			
			struct v2f
			{
				float4 vertex : SV_POSITION;
				float4 ase_texcoord : TEXCOORD0;
				UNITY_VERTEX_OUTPUT_STEREO
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};

			uniform float4 _iMouse;
			uniform sampler2D _MainTex;
			uniform float _Float2;
			uniform float _Float1;
			float3 Draw1_g3( float2 fragCoord , float4 iMouse , float2 iResolution , sampler2D Tex , float bZ , out float bout , out float bbrush , out float3 vxz )
			{
				float4 fragColor;
				float brushOpacity = clamp(iMouse.w * 3,0,1);
				float brushSize = (bZ*length(iResolution.xy)/256);
				if (fragCoord.x+fragCoord.y < 1) {
				  return fragColor = float4(iMouse.xy,1,0.);
				}
				float2 uv = fragCoord/iResolution.xy;
				float4 v = tex2D(Tex, uv);
				float2 currDelta = iMouse.xy-fragCoord;
				float linee;
				float brush;
				if (iMouse.z > 0) {
				    
				    float3 old = tex2D(Tex, float2(.5/iResolution.xy)).xyz;
				    old.xy = lerp(iMouse.xy,old.xy,old.z);
				    float2 oldDelta = fragCoord-old.xy, newDelta = iMouse.xy-old.xy;
				    float2 lon = old.xy - iMouse.xy;
				    float linlon =min(1000 , pow(lon.x * lon.x + lon.y * lon.y , 0.5));
				    float perlon = 1 - linlon / 1200 ; 
				    float len = length(currDelta)-brushSize * perlon;
				    len *= perlon;
				    
				   linee =length(oldDelta-newDelta*clamp(dot(oldDelta,newDelta)/
					dot(newDelta,newDelta),0.,1.))-brushSize * perlon;
				    
				     brush = brushOpacity*clamp(-linee/brushSize,0.,1.) * perlon;
				    brush =max(brush , brushOpacity*clamp(-len/brushSize,0.,1.));
				}
					bout = smoothstep(0.1 , 0.5 ,brush);
					bbrush = brush;
					vxz = v.xyz;
				    v.xz =max(v.xz, bout);
				    v.y = max(v.y , brush);
				fragColor = v;
				return fragColor;
			}
			
			float hash( float n )
			{
				return frac(sin(dot(float2(n,n) ,float2(12.9898,78.233))) * 43758.5453);  
			}
			
			float2 turbulence( float2 uv , float2 speed , float iTime )
			{
				float2 turb;
				turb.x = sin(uv.x);
				turb.y = cos(uv.y);
				for(int i = 0; i < 10; i++)
				{
				    float ifloat = 1.0 + float(i);
				    float ifloat1 = ifloat + 1;
				    float ifloat2 = ifloat + 100; 
				    
				    float r1 = hash(ifloat1)*2.0-1.0;
				    float r2 = hash(ifloat2)*2.0-1.0;
				    
				    float2 turb2;
				    turb2.x = sin(uv.x*(1.0 + r1*30) + turb.y*0.3*ifloat + iTime*speed.x*r2);
				    turb2.y = cos(uv.y*(1.0 + r1*30) + turb.x*0.3*ifloat + iTime*speed.y*r2);
				    
				    turb.x = lerp(turb.x, turb2.x, 0.5);
				    turb.y = lerp(turb.y, turb2.y, 0.5);
				}
				return turb;
			}
			
			
			v2f vert ( appdata v )
			{
				v2f o;
				UNITY_SETUP_INSTANCE_ID(v);
				UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);
				UNITY_TRANSFER_INSTANCE_ID(v, o);

				float4 ase_clipPos = UnityObjectToClipPos(v.vertex);
				float4 screenPos = ComputeScreenPos(ase_clipPos);
				o.ase_texcoord = screenPos;
				
				
				v.vertex.xyz +=  float3(0,0,0) ;
				o.vertex = UnityObjectToClipPos(v.vertex);
				return o;
			}
			
			fixed4 frag (v2f i ) : SV_Target
			{
				UNITY_SETUP_INSTANCE_ID(i);
				fixed4 finalColor;
				float4 screenPos = i.ase_texcoord;
				float4 ase_screenPosNorm = screenPos/screenPos.w;
				ase_screenPosNorm.z = ( UNITY_NEAR_CLIP_VALUE >= 0 ) ? ase_screenPosNorm.z : ase_screenPosNorm.z * 0.5 + 0.5;
				float2 appendResult2_g2 = (float2(ase_screenPosNorm.x , ase_screenPosNorm.y));
				float2 appendResult3_g2 = (float2(_ScreenParams.x , _ScreenParams.y));
				float2 fragCoord1_g3 = ( appendResult2_g2 * appendResult3_g2 );
				float4 iMouse1_g3 = _iMouse;
				float2 appendResult196 = (float2(_ScreenParams.x , _ScreenParams.y));
				float2 iResolution1_g3 = appendResult196;
				sampler2D Tex1_g3 = _MainTex;
				float clampResult360 = clamp( ( _iMouse.w * 3.0 ) , 0.0 , 1.0 );
				float bZ1_g3 = ( clampResult360 * _Float2 );
				float bout1_g3 = 0.0;
				float bbrush1_g3 = 0.0;
				float3 vxz1_g3 = float3( 0,0,0 );
				float3 localDraw1_g3 = Draw1_g3( fragCoord1_g3 , iMouse1_g3 , iResolution1_g3 , Tex1_g3 , bZ1_g3 , bout1_g3 , bbrush1_g3 , vxz1_g3 );
				float3 temp_output_265_0 = (localDraw1_g3).xyz;
				float2 appendResult222 = (float2(ase_screenPosNorm.x , ase_screenPosNorm.y));
				float2 uv226 = ( float2( 2,1 ) * appendResult222 );
				float2 speed226 = float2( 0,0 );
				float iTime226 = 0.0;
				float2 localturbulence226 = turbulence( uv226 , speed226 , iTime226 );
				float2 temp_output_438_0 = ( float2( 0.001,0.001 ) * float2( 2,1 ) );
				float2 appendResult420 = (float2(ase_screenPosNorm.x , ase_screenPosNorm.y));
				float4 tex2DNode439 = tex2D( _MainTex, appendResult420 );
				float2 appendResult435 = (float2(( tex2D( _MainTex, ( temp_output_438_0 + appendResult420 ) ).g + ( tex2D( _MainTex, ( appendResult420 - temp_output_438_0 ) ).g * -1.0 ) + tex2DNode439.g ) , ( tex2D( _MainTex, ( appendResult420 + ( temp_output_438_0 * float2( -1,0 ) ) ) ).g + ( tex2D( _MainTex, ( appendResult420 + ( temp_output_438_0 * float2( 0,-1 ) ) ) ).g * -1.0 ) + tex2DNode439.g )));
				float2 G387 = appendResult435;
				float temp_output_399_0 = ( _Float1 * tex2D( _MainTex, ( ( ( ( ( localturbulence226 * 0.5 ) + 0.5 ) * 0.001 ) + ( 0.001 * -0.5 ) ) + appendResult222 + ( G387 * 0.01 ) ) ).b );
				float3 appendResult401 = (float3(temp_output_399_0 , ( (localDraw1_g3).y * _Float1 ) , temp_output_399_0));
				#ifdef _KEYWORD0_ON
				float3 staticSwitch215 = max( appendResult401 , temp_output_265_0 );
				#else
				float3 staticSwitch215 = ( float3( 0,0,0 ) * temp_output_265_0 );
				#endif
				
				
				finalColor = float4( staticSwitch215 , 0.0 );
				return finalColor;
			}
			ENDCG
		}
	}
	CustomEditor "ASEMaterialInspector"
	
	
}
/*ASEBEGIN
Version=16100
2;4;1844;1051;-459.1847;-998.7502;1;True;True
Node;AmplifyShaderEditor.Vector2Node;422;1048.763,1617.16;Float;False;Constant;_Vector2;Vector 2;5;0;Create;True;0;0;False;0;0.001,0.001;0,0;0;3;FLOAT2;0;FLOAT;1;FLOAT;2
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;438;1303.794,1591.851;Float;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;2,1;False;1;FLOAT2;0
Node;AmplifyShaderEditor.ScreenPosInputsNode;419;1011.623,1382.781;Float;False;0;False;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.DynamicAppendNode;420;1215.458,1409.297;Float;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;429;1516.176,1732.707;Float;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,-1;False;1;FLOAT2;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;427;1511.176,1598.707;Float;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;-1,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.SimpleSubtractOpNode;426;1685.176,1372.707;Float;False;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.SimpleAddOpNode;430;1729.176,1741.707;Float;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.TexturePropertyNode;198;1521.938,382.6434;Float;True;Property;_MainTex;MainTex;1;0;Create;True;0;0;False;0;None;None;False;white;Auto;Texture2D;0;1;SAMPLER2D;0
Node;AmplifyShaderEditor.ScreenPosInputsNode;219;292.0735,930.1062;Float;False;0;False;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SamplerNode;425;1916.263,1730.66;Float;True;Property;_TextureSample4;Texture Sample 4;5;0;Create;True;0;0;False;0;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;6;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SimpleAddOpNode;428;1706.176,1554.707;Float;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.SimpleAddOpNode;421;1672.763,1256.16;Float;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.SamplerNode;423;1920.657,1330.571;Float;True;Property;_TextureSample2;Texture Sample 2;5;0;Create;True;0;0;False;0;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;6;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.Vector2Node;495;435.3359,697.4565;Float;False;Constant;_Vector0;Vector 0;5;0;Create;True;0;0;False;0;2,1;0,0;0;3;FLOAT2;0;FLOAT;1;FLOAT;2
Node;AmplifyShaderEditor.DynamicAppendNode;222;542.9077,959.6223;Float;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.SamplerNode;424;1917.763,1518.089;Float;True;Property;_TextureSample3;Texture Sample 3;5;0;Create;True;0;0;False;0;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;6;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SamplerNode;439;1932.01,930.2444;Float;True;Property;_TextureSample5;Texture Sample 5;5;0;Create;True;0;0;False;0;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;6;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;432;2278.176,1779.707;Float;False;2;2;0;FLOAT;0;False;1;FLOAT;-1;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;431;2250.397,1376.272;Float;False;2;2;0;FLOAT;0;False;1;FLOAT;-1;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;494;758.3359,796.4565;Float;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.SamplerNode;418;1916.142,1142.985;Float;True;Property;_TextureSample1;Texture Sample 1;5;0;Create;True;0;0;False;0;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;6;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SimpleAddOpNode;433;2497.742,1303.69;Float;False;3;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.CustomExpressionNode;226;958.4135,731.0249;Float;False;float2 turb@$turb.x = sin(uv.x)@$turb.y = cos(uv.y)@$for(int i = 0@ i < 10@ i++)${$    float ifloat = 1.0 + float(i)@$    float ifloat1 = ifloat + 1@$    float ifloat2 = ifloat + 100@ $    $    float r1 = hash(ifloat1)*2.0-1.0@$    float r2 = hash(ifloat2)*2.0-1.0@$    $    float2 turb2@$    turb2.x = sin(uv.x*(1.0 + r1*30) + turb.y*0.3*ifloat + iTime*speed.x*r2)@$    turb2.y = cos(uv.y*(1.0 + r1*30) + turb.x*0.3*ifloat + iTime*speed.y*r2)@$    $    turb.x = lerp(turb.x, turb2.x, 0.5)@$    turb.y = lerp(turb.y, turb2.y, 0.5)@$}$return turb@;2;False;3;True;uv;FLOAT2;0,0;In;;Float;True;speed;FLOAT2;0,0;In;;Float;True;iTime;FLOAT;0;In;;Float;turbulence;False;False;1;257;3;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.RangedFloatNode;223;954.7904,874.1169;Float;False;Constant;_05;0.5;0;0;Create;True;0;0;False;0;0.5;0;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleAddOpNode;434;2500.809,1608.564;Float;False;3;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.DynamicAppendNode;435;2653.833,1489.892;Float;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;227;1174.217,731.5546;Float;False;2;2;0;FLOAT2;0,0;False;1;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.Vector4Node;194;636.9996,53.9905;Float;False;Property;_iMouse;iMouse;0;0;Create;True;0;0;False;0;0,0,0,0;1621.392,533.7838,-1,-0.9921476;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.RangedFloatNode;230;1298.936,975.8837;Float;False;Constant;_Float0;Float 0;0;0;Create;True;0;0;False;0;0.001;0;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.RegisterLocalVarNode;387;2869.068,1449.016;Float;False;G;-1;True;1;0;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.SimpleAddOpNode;231;1336.454,733.5651;Float;False;2;2;0;FLOAT2;0,0;False;1;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.GetLocalVarNode;391;652.7725,1146.635;Float;False;387;G;1;0;OBJECT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.RangedFloatNode;394;803.7725,1294.635;Float;False;Constant;_Float4;Float 4;5;0;Create;True;0;0;False;0;0.01;0;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;235;1540.359,894.3004;Float;False;2;2;0;FLOAT;0;False;1;FLOAT;-0.5;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;240;1520.473,724.5299;Float;False;2;2;0;FLOAT2;0,0;False;1;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;361;1006.444,267.787;Float;False;2;2;0;FLOAT;0;False;1;FLOAT;3;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleAddOpNode;397;1686.032,726.1691;Float;False;2;2;0;FLOAT2;0,0;False;1;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.ClampOpNode;360;1302.444,334.787;Float;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;1;False;1;FLOAT;0
Node;AmplifyShaderEditor.ScreenParams;195;1334.938,-0.3565979;Float;False;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;393;1094.772,1150.635;Float;False;2;2;0;FLOAT2;0,0;False;1;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.RangedFloatNode;286;1619.89,292.0641;Float;False;Property;_Float2;Float 2;4;0;Create;True;0;0;False;0;0.9;3;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.FunctionNode;293;1609.938,-77.35663;Float;False;ScreenUV;-1;;2;e49fe2f436abc9d43bf8068a1dc44841;0;0;1;FLOAT2;0
Node;AmplifyShaderEditor.SimpleAddOpNode;242;1889.865,757.4474;Float;False;3;3;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;2;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;358;1550.444,172.787;Float;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.DynamicAppendNode;196;1595.938,40.6434;Float;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.RangedFloatNode;251;1873.074,540.1763;Float;False;Property;_Float1;Float 1;3;0;Create;True;0;0;False;0;0.9;0.96;0.7;1;0;1;FLOAT;0
Node;AmplifyShaderEditor.SamplerNode;249;2074.897,709.4653;Float;True;Property;_TextureSample0;Texture Sample 0;1;0;Create;True;0;0;False;0;37a5ff65fbc02af4caf2d02a6eed2778;37a5ff65fbc02af4caf2d02a6eed2778;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;6;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.FunctionNode;517;1887.462,70.28535;Float;False;brushpen;-1;;3;65f3bf4e88954c246938a4a5f777e07d;0;5;2;FLOAT2;0,0;False;3;FLOAT4;0,0,0,0;False;4;FLOAT2;0,0;False;6;FLOAT;0;False;5;SAMPLER2D;0;False;2;FLOAT3;0;FLOAT;8
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;399;2347.434,482.9837;Float;False;2;2;0;FLOAT;0;False;1;FLOAT;0.95;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;253;2321.656,355.4305;Float;False;2;2;0;FLOAT;0;False;1;FLOAT;0.95;False;1;FLOAT;0
Node;AmplifyShaderEditor.DynamicAppendNode;401;2519.434,414.9837;Float;False;FLOAT3;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT3;0
Node;AmplifyShaderEditor.ComponentMaskNode;265;2170.467,98.5175;Float;False;True;True;True;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0
Node;AmplifyShaderEditor.SimpleMaxOpNode;258;2561.671,229.8586;Float;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;214;2562.396,10.5791;Float;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0
Node;AmplifyShaderEditor.CustomExpressionNode;257;961.3405,602.0636;Float;False;return frac(sin(dot(float2(n,n) ,float2(12.9898,78.233))) * 43758.5453)@  $;1;False;1;True;n;FLOAT;0;In;;Float;hash;False;False;0;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;364;2715.176,-71.60046;Float;False;Constant;_Float3;Float 3;5;0;Create;True;0;0;False;0;0;0;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.StaticSwitch;215;2767.303,104.9051;Float;False;Property;_Keyword0;Keyword 0;2;0;Create;True;0;0;False;0;0;0;1;True;;Toggle;2;Key0;Key1;9;1;FLOAT3;0,0,0;False;0;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;4;FLOAT3;0,0,0;False;5;FLOAT3;0,0,0;False;6;FLOAT3;0,0,0;False;7;FLOAT3;0,0,0;False;8;FLOAT3;0,0,0;False;1;FLOAT3;0
Node;AmplifyShaderEditor.TemplateMultiPassMasterNode;8;3034.055,101.729;Float;False;True;2;Float;ASEMaterialInspector;0;1;brushpen;0770190933193b94aaa3065e307002fa;0;0;Unlit;2;True;0;1;False;-1;0;False;-1;0;1;False;-1;0;False;-1;True;0;False;-1;0;False;-1;True;0;False;-1;True;True;True;True;True;0;False;-1;True;False;255;False;-1;255;False;-1;255;False;-1;7;False;-1;1;False;-1;1;False;-1;1;False;-1;7;False;-1;1;False;-1;1;False;-1;1;False;-1;True;1;False;-1;True;3;False;-1;True;True;0;False;-1;0;False;-1;True;1;RenderType=Opaque=RenderType;True;2;0;False;False;False;False;False;False;False;False;False;False;0;;0;0;Standard;0;2;0;FLOAT4;0,0,0,0;False;1;FLOAT3;0,0,0;False;0
WireConnection;438;0;422;0
WireConnection;420;0;419;1
WireConnection;420;1;419;2
WireConnection;429;0;438;0
WireConnection;427;0;438;0
WireConnection;426;0;420;0
WireConnection;426;1;438;0
WireConnection;430;0;420;0
WireConnection;430;1;429;0
WireConnection;425;0;198;0
WireConnection;425;1;430;0
WireConnection;428;0;420;0
WireConnection;428;1;427;0
WireConnection;421;0;438;0
WireConnection;421;1;420;0
WireConnection;423;0;198;0
WireConnection;423;1;426;0
WireConnection;222;0;219;1
WireConnection;222;1;219;2
WireConnection;424;0;198;0
WireConnection;424;1;428;0
WireConnection;439;0;198;0
WireConnection;439;1;420;0
WireConnection;432;0;425;2
WireConnection;431;0;423;2
WireConnection;494;0;495;0
WireConnection;494;1;222;0
WireConnection;418;0;198;0
WireConnection;418;1;421;0
WireConnection;433;0;418;2
WireConnection;433;1;431;0
WireConnection;433;2;439;2
WireConnection;226;0;494;0
WireConnection;434;0;424;2
WireConnection;434;1;432;0
WireConnection;434;2;439;2
WireConnection;435;0;433;0
WireConnection;435;1;434;0
WireConnection;227;0;226;0
WireConnection;227;1;223;0
WireConnection;387;0;435;0
WireConnection;231;0;227;0
WireConnection;231;1;223;0
WireConnection;235;0;230;0
WireConnection;240;0;231;0
WireConnection;240;1;230;0
WireConnection;361;0;194;4
WireConnection;397;0;240;0
WireConnection;397;1;235;0
WireConnection;360;0;361;0
WireConnection;393;0;391;0
WireConnection;393;1;394;0
WireConnection;242;0;397;0
WireConnection;242;1;222;0
WireConnection;242;2;393;0
WireConnection;358;0;360;0
WireConnection;358;1;286;0
WireConnection;196;0;195;1
WireConnection;196;1;195;2
WireConnection;249;0;198;0
WireConnection;249;1;242;0
WireConnection;517;2;293;0
WireConnection;517;3;194;0
WireConnection;517;4;196;0
WireConnection;517;6;358;0
WireConnection;517;5;198;0
WireConnection;399;0;251;0
WireConnection;399;1;249;3
WireConnection;253;0;517;8
WireConnection;253;1;251;0
WireConnection;401;0;399;0
WireConnection;401;1;253;0
WireConnection;401;2;399;0
WireConnection;265;0;517;0
WireConnection;258;0;401;0
WireConnection;258;1;265;0
WireConnection;214;1;265;0
WireConnection;215;1;214;0
WireConnection;215;0;258;0
WireConnection;8;0;215;0
ASEEND*/
//CHKSM=EAB6D4B2E9CC197EF8B2BC8A78BE33348888FA26