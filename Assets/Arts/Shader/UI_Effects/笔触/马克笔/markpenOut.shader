// Made with Amplify Shader Editor
// Available at the Unity Asset Store - http://u3d.as/y3X 
Shader "markpenOut"
{
	Properties
	{
		_MainTex("MainTex", 2D) = "white" {}
		_Nus("Nus", Float) = 3
		_V("V", Range( -0.01 , 0.01)) = 0
		_U("U", Range( -0.01 , 0.01)) = 0
		_BGTex1("BGTex", 2D) = "white" {}
		_Color1("Color", Color) = (0,0,0,0)
		[HideInInspector] _texcoord( "", 2D ) = "white" {}

	}
	
	SubShader
	{
		
		
		Tags { "RenderType"="Opaque" }
	LOD 0

		CGINCLUDE
		#pragma target 3.0
		ENDCG
		Blend Off
		AlphaToMask Off
		Cull Back
		ColorMask RGBA
		ZWrite On
		ZTest LEqual
		Offset 0 , 0
		
		
		
		Pass
		{
			Name "Unlit"
			Tags { "LightMode"="ForwardBase" }
			CGPROGRAM

			

			#ifndef UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX
			//only defining to not throw compilation error over Unity 5.5
			#define UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(input)
			#endif
			#pragma vertex vert
			#pragma fragment frag
			#pragma multi_compile_instancing
			#include "UnityCG.cginc"
			

			struct appdata
			{
				float4 vertex : POSITION;
				float4 color : COLOR;
				float4 ase_texcoord : TEXCOORD0;
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};
			
			struct v2f
			{
				float4 vertex : SV_POSITION;
				#ifdef ASE_NEEDS_FRAG_WORLD_POSITION
				float3 worldPos : TEXCOORD0;
				#endif
				float4 ase_texcoord1 : TEXCOORD1;
				float4 ase_texcoord2 : TEXCOORD2;
				UNITY_VERTEX_INPUT_INSTANCE_ID
				UNITY_VERTEX_OUTPUT_STEREO
			};

			uniform float4 _Color1;
			uniform sampler2D _BGTex1;
			uniform float4 _BGTex1_ST;
			uniform sampler2D _MainTex;
			uniform half _Nus;
			uniform float _U;
			uniform float _V;
			float forfor352( sampler2D Tex, int Nus, float2 one, float2 uv )
			{
				float outt = 0;
				for(int i = 0 ; i < Nus ; i ++){
				  float2 uvm = uv + (one * i);
				  float2 uvn = uv - (one * i);
				  outt += tex2D(Tex , uvm).y;
				  outt += tex2D(Tex , uvn).y;
				}
				return outt;
			}
			

			
			v2f vert ( appdata v )
			{
				v2f o;
				UNITY_SETUP_INSTANCE_ID(v);
				UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);
				UNITY_TRANSFER_INSTANCE_ID(v, o);

				float4 ase_clipPos = UnityObjectToClipPos(v.vertex);
				float4 screenPos = ComputeScreenPos(ase_clipPos);
				o.ase_texcoord2 = screenPos;
				
				o.ase_texcoord1.xy = v.ase_texcoord.xy;
				
				//setting value to unused interpolator channels and avoid initialization warnings
				o.ase_texcoord1.zw = 0;
				float3 vertexValue = float3(0, 0, 0);
				#if ASE_ABSOLUTE_VERTEX_POS
				vertexValue = v.vertex.xyz;
				#endif
				vertexValue = vertexValue;
				#if ASE_ABSOLUTE_VERTEX_POS
				v.vertex.xyz = vertexValue;
				#else
				v.vertex.xyz += vertexValue;
				#endif
				o.vertex = UnityObjectToClipPos(v.vertex);

				#ifdef ASE_NEEDS_FRAG_WORLD_POSITION
				o.worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
				#endif
				return o;
			}
			
			fixed4 frag (v2f i ) : SV_Target
			{
				UNITY_SETUP_INSTANCE_ID(i);
				UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(i);
				fixed4 finalColor;
				#ifdef ASE_NEEDS_FRAG_WORLD_POSITION
				float3 WorldPosition = i.worldPos;
				#endif
				float2 uv_BGTex1 = i.ase_texcoord1.xy * _BGTex1_ST.xy + _BGTex1_ST.zw;
				sampler2D Tex352 = _MainTex;
				int Nus352 = (int)_Nus;
				float2 appendResult342 = (float2(_U , _V));
				float2 one352 = appendResult342;
				float4 screenPos = i.ase_texcoord2;
				float4 ase_screenPosNorm = screenPos / screenPos.w;
				ase_screenPosNorm.z = ( UNITY_NEAR_CLIP_VALUE >= 0 ) ? ase_screenPosNorm.z : ase_screenPosNorm.z * 0.5 + 0.5;
				float2 appendResult338 = (float2(ase_screenPosNorm.x , ase_screenPosNorm.y));
				float2 uv352 = appendResult338;
				float localforfor352 = forfor352( Tex352 , Nus352 , one352 , uv352 );
				float clampResult346 = clamp( localforfor352 , 0.0 , 1.0 );
				float4 lerpResult360 = lerp( _Color1 , tex2D( _BGTex1, uv_BGTex1 ) , ( 1.0 - clampResult346 ));
				
				
				finalColor = lerpResult360;
				return finalColor;
			}
			ENDCG
		}
	}
	CustomEditor "ASEMaterialInspector"
	
	
}
/*ASEBEGIN
Version=18909
6.666667;100.6667;2546.667;1278.333;1043.94;930.5353;1;True;True
Node;AmplifyShaderEditor.ScreenPosInputsNode;337;-903.339,278.842;Float;False;0;False;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.RangedFloatNode;357;-922.7112,146.0277;Float;False;Property;_V;V;2;0;Create;True;0;0;0;False;0;False;0;0;-0.01;0.01;0;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;356;-972.1112,29.02773;Float;False;Property;_U;U;3;0;Create;True;0;0;0;False;0;False;0;0;-0.01;0.01;0;1;FLOAT;0
Node;AmplifyShaderEditor.DynamicAppendNode;342;-583.7468,110.6921;Inherit;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.DynamicAppendNode;338;-660.5549,291.4423;Inherit;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.TexturePropertyNode;354;-900.2513,-331.0756;Float;True;Property;_MainTex;MainTex;0;0;Create;True;0;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1
Node;AmplifyShaderEditor.RangedFloatNode;355;-672.5131,-76.3231;Half;False;Property;_Nus;Nus;1;0;Create;True;0;0;0;False;0;False;3;3;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.CustomExpressionNode;352;-153.3452,-105.5659;Float;False;float outt = 0@$for(int i = 0 @ i < Nus @ i ++){$  float2 uvm = uv + (one * i)@$  float2 uvn = uv - (one * i)@$  outt += tex2D(Tex , uvm).y@$  outt += tex2D(Tex , uvn).y@$}$return outt@;1;Create;4;True;Tex;SAMPLER2D;;In;;Float;False;True;Nus;INT;0;In;;Float;False;True;one;FLOAT2;0,0;In;;Float;False;True;uv;FLOAT2;0,0;In;;Float;False;forfor;True;False;0;;False;4;0;SAMPLER2D;;False;1;INT;0;False;2;FLOAT2;0,0;False;3;FLOAT2;0,0;False;1;FLOAT;0
Node;AmplifyShaderEditor.ClampOpNode;346;261.4658,-88.96839;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;1;False;1;FLOAT;0
Node;AmplifyShaderEditor.SamplerNode;358;-173.0163,-544.2629;Inherit;True;Property;_BGTex1;BGTex;4;0;Create;True;0;0;0;False;0;False;-1;None;8c77b67cd241cd5488518d9daa23397e;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.ColorNode;359;406.9837,-601.2628;Float;False;Property;_Color1;Color;5;0;Create;True;0;0;0;False;0;False;0,0,0,0;0.2569865,0.2614118,0.5188679,0;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.OneMinusNode;291;512.6053,-123.0417;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.LerpOp;360;655.9837,-323.2629;Inherit;False;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;FLOAT;0;False;1;COLOR;0
Node;AmplifyShaderEditor.TemplateMultiPassMasterNode;8;943.965,-84.90981;Float;False;True;-1;2;ASEMaterialInspector;0;1;markpenOut;0770190933193b94aaa3065e307002fa;True;Unlit;0;0;Unlit;2;False;True;0;1;False;-1;0;False;-1;0;1;False;-1;0;False;-1;True;0;False;-1;0;False;-1;False;False;False;False;False;False;False;False;False;True;0;False;-1;False;True;0;False;-1;False;True;True;True;True;True;0;False;-1;False;False;False;False;False;False;False;True;False;255;False;-1;255;False;-1;255;False;-1;7;False;-1;1;False;-1;1;False;-1;1;False;-1;7;False;-1;1;False;-1;1;False;-1;1;False;-1;False;True;1;False;-1;True;3;False;-1;True;True;0;False;-1;0;False;-1;True;1;RenderType=Opaque=RenderType;True;2;0;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;True;1;LightMode=ForwardBase;False;0;;0;0;Standard;1;Vertex Position,InvertActionOnDeselection;1;0;1;True;False;;False;0
WireConnection;342;0;356;0
WireConnection;342;1;357;0
WireConnection;338;0;337;1
WireConnection;338;1;337;2
WireConnection;352;0;354;0
WireConnection;352;1;355;0
WireConnection;352;2;342;0
WireConnection;352;3;338;0
WireConnection;346;0;352;0
WireConnection;291;0;346;0
WireConnection;360;0;359;0
WireConnection;360;1;358;0
WireConnection;360;2;291;0
WireConnection;8;0;360;0
ASEEND*/
//CHKSM=4E09EDE27A60D8F50A1EAA47989A33BB567E473D