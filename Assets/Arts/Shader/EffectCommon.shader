Shader "Bioum/Effect/Common" 
{
	Properties 
	{
		_MainTex ("Main Tex", 2D) = "white" {}
        [Enum(Multiply,0,Add,1)] _TexBlendMode("Tex Blend Mode", float) = 0
		_MainTexRotateAngle ("Rotate Angle", Range(0,360)) = 0
		_MainTexUVAni ("MainTex Ani", Vector) = (0,0,0,0)
		_DistortMap ("Distort Tex", 2D) = "grey" {}
		_MaskMap ("Mask Tex", 2D) = "white" {}
		
		_DistortFactor ("扭曲强度", Range(0,0.5) ) = 0.15
		_DistortUVAni ("Distort Ani", Vector) = (0,0,0,0)
		
		[HDR]_TintColor("Color", Color) = (0.5, 0.5, 0.5, 1)  
        _Cutoff("cutoff", Range(0,1)) = 0.5
		
		
		_DissolveFactor("dissolve factor", Range(0.1,1.5)) = 0.1
		_DissolveEdge("dissolve Edge", Range(0,1)) = 0.1
		_DissolveSoft("dissolve Soft", Range(0.01, 0.49)) = 0.2
		[HDR]_DissolveEdgeColor("dissolve Edge Color", color) = (1,1,1,1)
		_DissolveMap ("Dissolve Tex", 2D) = "white" {}
		
		
        _AniSheetSpeed("序列帧速度", float) = 1
        _AniSheetTile("序列帧规格", vector) = (4,4,0,0)
		
		[Toggle(ENABLE_DISTORT)] _Distort ("扭曲", Float) = 0
		[Toggle(ENABLE_MASK)] _Mask ("遮罩", Float) = 0
		[Toggle(ENABLE_DISSOLVE)] _Dissolve ("Dissolve", Float) = 0
		[Toggle(ISPARTICLE)] _IsParticle ("is particle", Float) = 0
		[Toggle(_ALPHATEST_ON)] _ALPHATEST_ON ("AlphaTest", Float) = 0
		[Toggle(SOFT_EDGE)] _Soft_Edge ("软粒子", Float) = 0
		[Toggle(ANIMATION_SHEET)] _AnimationSheet ("序列帧", Float) = 0
		
		[HideInInspector] _BlendMode ("__BlendMode", Float) = 0
		[HideInInspector] _SrcBlend ("__src", Float) = 1
		[HideInInspector] _DstBlend ("__dst", Float) = 10
		[HideInInspector] _CullMode ("__CullMode", Float) = 0
		[HideInInspector] _Cull ("__Cull", Float) = 2
		[HideInInspector] _ZWrite ("__ZWrite", Float) = 0
	}
	SubShader 
	{
		Tags {"IgnoreProjector"="True" "Queue"="Transparent" "RenderType"="Transparent" "PreviewType"="Plane"}
		Pass 
		{
			Blend [_SrcBlend] [_DstBlend]
			ZWrite [_ZWrite]
			Cull [_Cull]
			
			HLSLPROGRAM
			#pragma vertex vert
			#pragma fragment frag
			#include "../Shader/ShaderLibrary/Common.hlsl"
			#pragma target 3.5
			
			#pragma shader_feature __ ENABLE_DISTORT
			#pragma multi_compile __ ENABLE_MASK
			#pragma multi_compile __ ENABLE_DISSOLVE
			#pragma multi_compile __ ISPARTICLE
			#pragma multi_compile __ ANIMATION_SHEET
			#pragma multi_compile __ _MULTIPLY_ON _ALPHATEST_ON

			sampler2D _MainTex;
            sampler2D _DistortMap; 
            sampler2D _MaskMap;
            sampler2D _DissolveMap;

			half4 _MainTex_ST;
			half4 _MainTexUVAni;
            int _TexBlendMode;
            half _Cutoff;
                    
            half4 _DistortMap_ST;
            half4 _DistortUVAni;
            half _DistortFactor;

            half4 _MaskMap_ST;
            half4 _DissolveMap_ST;
            half _DissolveFactor;
            half _DissolveEdge;
            half _DissolveSoft;
            half4 _DissolveEdgeColor;
			
			half4 _TintColor;
			float _MainTexRotateAngle;

			#define PI          3.14159265358979323846
			#define TWO_PI      6.28318530717958647693
			

            #if ANIMATION_SHEET
                half _AniSheetSpeed;
                half4 _AniSheetTile;
            #endif

			float2 rotate(float2 uv, float angle)
            {
                float a = angle / 180 * PI;
                float2 pivot = float2(0.5, 0.5);
                float cosAngle = cos(a);
                float sinAngle = sin(a);
                float2x2 rot = float2x2(cosAngle, -sinAngle, sinAngle, cosAngle);
                uv = mul(rot,uv - pivot) + pivot;
                return uv;
            }
			
			struct VertexInput 
			{
				float4 positionOS : POSITION;
				half4 texcoord : TEXCOORD0;
				half4 vertexColor : COLOR;
				half3 normalOS : NORMAL;
				half4 CustomData1 : TEXCOORD1; //particle system custom data
				half2 DissloveUV: TEXCOORD2;
			};
			
			struct v2f 
			{
				float4 positionCS : SV_POSITION;
				float4 mainUV : TEXCOORD0;
				#if ENABLE_MASK || ENABLE_DISSOLVE
					half4 maskUV : TEXCOORD1;
				#endif
				#if ENABLE_DISTORT
					half2 distortUV : TEXCOORD2;
				#endif
	
				half4 vColor : COLOR;
				half4 CustomData1 : TEXCOORD3;
				half2 DissloveUV: TEXCOORD4;


			};
			
			v2f vert (VertexInput v) 
			{
				v2f o = (v2f)0;

                float4 positionWS = mul(UNITY_MATRIX_M, v.positionOS);
				o.positionCS = mul(UNITY_MATRIX_VP, positionWS);
				
				#if ISPARTICLE
					o.CustomData1 = half4(v.texcoord.zw, v.CustomData1.xy);
				#else
					o.CustomData1 = half4(1,1,1,1);
				#endif
				

                #if ANIMATION_SHEET
					float2 d = 1 / _AniSheetTile.xy;
                    float time = _Time.y * _AniSheetSpeed;
                    float2 xy = floor(float2(time, time * d.x));
                    float2 ani = frac(xy * d.xy);
                    o.mainUV.xy = v.texcoord.xy * d.xy + float2(ani.x, -ani.y);

                #else
					half2 mainUV = v.texcoord.xy * _MainTex_ST.xy;
					o.mainUV.xy += mainUV + _MainTex_ST.zw;
					o.mainUV.xy = ISPARTICLE ? mainUV + half2(o.CustomData1.y + _MainTex_ST.z,_MainTex_ST.w) : mainUV + _MainTex_ST.zw;
				#if !ISPARTICLE
					o.mainUV.xy += frac(float2(_MainTexUVAni.xy * _Time.y));
				#endif
                    o.mainUV.xy = rotate(o.mainUV.xy, _MainTexRotateAngle);
				
                #endif
                
				
				#if ENABLE_DISTORT
                    o.distortUV = v.texcoord.xy * _DistortMap_ST.xy;
					o.distortUV += o.distortUV + _DistortMap_ST.zw;
                    o.distortUV += frac(half4(_DistortUVAni * _Time.y));
				#endif
				
				#if ENABLE_MASK || ENABLE_DISSOLVE
                    o.maskUV = v.texcoord.xyxy * half4(_MaskMap_ST.xy, _DissolveMap_ST.xy);
					o.maskUV = ISPARTICLE ? o.maskUV + half4(o.CustomData1.z + _MaskMap_ST.z,_MaskMap_ST.w,o.CustomData1.z + _DissolveMap_ST.z,_DissolveMap_ST.w) : o.maskUV + half4(_MaskMap_ST.zw, _DissolveMap_ST.zw);;
				#endif
				
				o.vColor = v.vertexColor;
				
				return o;
			}
			
			half4 frag(v2f i) : SV_Target 
			{
				half4 _MainTexColor;
				_MainTexColor = tex2D(_MainTex, i.mainUV.xy);
				#if ENABLE_DISTORT
					half _DistortTex_var = tex2D(_DistortMap, i.distortUV.xy).r - 0.5;
					half distort = _DistortTex_var  * _DistortFactor * max(i.CustomData1.w, 1);
					half4 texUV = i.mainUV + distort;
					_MainTexColor = tex2D(_MainTex, texUV.xy);
				#endif
				
				#if ENABLE_DISSOLVE
					half ClipTex;
					#if !ENABLE_DISTORT
						ClipTex = tex2D(_DissolveMap, i.maskUV.zw).r;
					#else
						half2 dissolveUV = i.maskUV.zw + distort;
						ClipTex = tex2D(_DissolveMap, dissolveUV).r;
					#endif

                    half ClipAera, ClipAeraEdge;
                    half2 ClipAeraAndEdge;
				
					half2 f = half2(_DissolveFactor, _DissolveEdge) * max(i.CustomData1.x, 0.5);
					half2 clipFac = half2(f.x * 2 - 1, f.y);
					ClipAera = ClipTex - clipFac.x;
					ClipAeraEdge = ClipAera - clipFac.y;
					ClipAeraAndEdge = smoothstep(0.5 - _DissolveSoft, 0.5 + _DissolveSoft, half2(ClipAera, ClipAeraEdge));
				
				#endif
				
				half3 colorFactor = _MainTexColor.rgb * _TintColor.rgb * i.vColor.rgb;
				half alphaFactor = _MainTexColor.a * _TintColor.a * i.vColor.a;

				#if ENABLE_DISSOLVE
					colorFactor = lerp(_DissolveEdgeColor.rgb, colorFactor, ClipAeraAndEdge.y);
                    alphaFactor *= ClipAeraAndEdge.x;
				#endif
				
				
				#if	_ALPHATEST_ON
					clip(alphaFactor - _Cutoff);
				#endif
				
				half4 col;
				col.rgb = colorFactor;
				col.a = alphaFactor;
				
				
				#if ENABLE_MASK
					half4 masktex = tex2D(_MaskMap, i.maskUV.xy);
					half mask = min(masktex.r, masktex.a);
					col.a *= mask;
				#endif
				
				col.rgb *= col.a;
				
				#if _MULTIPLY_ON
					col = lerp(half4(0.5,0.5,0.5,0.5), col, col.a);
				#endif
				
				return col;
			}
			ENDHLSL
		}
	}
	CustomEditor "EffectCommonGUI"
}
